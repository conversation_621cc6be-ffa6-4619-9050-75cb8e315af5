package com.ramstechapp.latram.ui.emergency

import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.ramstechapp.latram.databinding.ActivityEmergencyLockScreenBinding
import com.ramstechapp.latram.data.model.EmergencyType
import com.ramstechapp.latram.service.EmergencyService
import com.ramstechapp.latram.ui.MainActivity
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class EmergencyLockScreenActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityEmergencyLockScreenBinding
    private var wakeLock: PowerManager.WakeLock? = null
    private var emergencyCountdown = 5
    private var isCountdownActive = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Setup for lock screen display
        setupLockScreenDisplay()
        
        binding = ActivityEmergencyLockScreenBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        acquireWakeLock()
    }
    
    private fun setupLockScreenDisplay() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        } else {
            @Suppress("DEPRECATION")
            window.addFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD
            )
        }
        
        // Make activity full screen
        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
        
        // Disable keyguard if possible
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            @Suppress("DEPRECATION")
            val keyguardLock = keyguardManager.newKeyguardLock("LATRAM_EMERGENCY")
            @Suppress("DEPRECATION")
            keyguardLock.disableKeyguard()
        }
    }
    
    private fun setupUI() {
        // Emergency button with countdown
        binding.emergencyButton.setOnClickListener {
            if (!isCountdownActive) {
                startEmergencyCountdown()
            }
        }
        
        // Quick emergency types
        binding.medicalButton.setOnClickListener {
            triggerEmergency(EmergencyType.MEDICAL, "Medical Emergency!")
        }
        
        binding.fireButton.setOnClickListener {
            triggerEmergency(EmergencyType.FIRE, "Fire Emergency!")
        }
        
        binding.policeButton.setOnClickListener {
            triggerEmergency(EmergencyType.POLICE, "Police Emergency!")
        }
        
        binding.accidentButton.setOnClickListener {
            triggerEmergency(EmergencyType.ACCIDENT, "Accident Emergency!")
        }
        
        // Cancel button
        binding.cancelButton.setOnClickListener {
            if (isCountdownActive) {
                cancelCountdown()
            } else {
                finish()
            }
        }
        
        // Open main app button
        binding.openAppButton.setOnClickListener {
            openMainApp()
        }
        
        // Update status
        updateStatusText()
    }
    
    private fun startEmergencyCountdown() {
        isCountdownActive = true
        emergencyCountdown = 5
        
        binding.emergencyButton.isEnabled = false
        binding.cancelButton.text = "CANCEL"
        
        lifecycleScope.launch {
            while (emergencyCountdown > 0 && isCountdownActive) {
                binding.countdownText.text = "Emergency alert in $emergencyCountdown seconds"
                binding.emergencyButton.text = emergencyCountdown.toString()
                
                delay(1000)
                emergencyCountdown--
            }
            
            if (isCountdownActive) {
                triggerEmergency(EmergencyType.GENERAL, "Emergency! I need help.")
            }
        }
    }
    
    private fun cancelCountdown() {
        isCountdownActive = false
        emergencyCountdown = 5
        
        binding.emergencyButton.isEnabled = true
        binding.emergencyButton.text = "EMERGENCY"
        binding.countdownText.text = "Press and hold for emergency alert"
        binding.cancelButton.text = "CLOSE"
    }
    
    private fun triggerEmergency(emergencyType: EmergencyType, message: String) {
        // Cancel countdown if active
        isCountdownActive = false
        
        // Start emergency service
        val intent = Intent(this, EmergencyService::class.java).apply {
            action = EmergencyService.ACTION_TRIGGER_EMERGENCY
            putExtra(EmergencyService.EXTRA_EMERGENCY_MESSAGE, message)
            putExtra(EmergencyService.EXTRA_EMERGENCY_TYPE, emergencyType.name)
        }
        startForegroundService(intent)
        
        // Update UI
        binding.statusText.text = "🚨 Emergency Alert Triggered!"
        binding.statusText.setTextColor(getColor(android.R.color.holo_red_light))
        binding.emergencyButton.isEnabled = false
        
        // Auto-close after a delay
        lifecycleScope.launch {
            delay(3000)
            finish()
        }
    }
    
    private fun openMainApp() {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        startActivity(intent)
        finish()
    }
    
    private fun updateStatusText() {
        // This could check emergency contacts, permissions, etc.
        binding.statusText.text = "Emergency Quick Access Ready"
    }
    
    private fun acquireWakeLock() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
            "LATRAM:EmergencyLockScreen"
        )
        wakeLock?.acquire(30000) // Keep screen on for 30 seconds
    }
    
    override fun onDestroy() {
        super.onDestroy()
        wakeLock?.release()
        isCountdownActive = false
    }
    
    override fun onBackPressed() {
        if (isCountdownActive) {
            cancelCountdown()
        } else {
            super.onBackPressed()
        }
    }
}
