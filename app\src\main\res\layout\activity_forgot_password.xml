<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    tools:context=".ui.auth.ForgotPasswordActivity">

    <!-- Back Button -->
    <ImageButton
        android:id="@+id/backButton"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_arrow_back"
        android:contentDescription="Back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/on_surface" />

    <!-- Logo -->
    <ImageView
        android:id="@+id/forgotPasswordLogo"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="80dp"
        android:src="@drawable/ic_lock"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/backButton"
        app:tint="@color/primary" />

    <!-- Title -->
    <TextView
        android:id="@+id/forgotPasswordTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="Forgot Password?"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
        android:textColor="@color/on_surface"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forgotPasswordLogo" />

    <!-- Subtitle -->
    <TextView
        android:id="@+id/forgotPasswordSubtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="Enter your email address and we'll send you instructions to reset your password"
        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
        android:textColor="@color/on_surface_variant"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forgotPasswordTitle" />

    <!-- Email Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/emailInputLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:hint="Email Address"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/forgotPasswordSubtitle"
        app:startIconDrawable="@drawable/ic_email">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/emailInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textEmailAddress" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Send Reset Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/sendResetButton"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginTop="32dp"
        android:text="Send Reset Instructions"
        android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
        app:cornerRadius="28dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emailInputLayout" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/sendResetButton"
        app:layout_constraintEnd_toEndOf="@+id/sendResetButton"
        app:layout_constraintStart_toStartOf="@+id/sendResetButton"
        app:layout_constraintTop_toTopOf="@+id/sendResetButton" />

    <!-- Success Layout -->
    <LinearLayout
        android:id="@+id/successLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sendResetButton">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginBottom="16dp"
            android:src="@drawable/ic_check_circle"
            app:tint="@color/success_green" />

        <TextView
            android:id="@+id/successMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Reset instructions sent!"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textColor="@color/on_surface" />

    </LinearLayout>

    <!-- Back to Login -->
    <TextView
        android:id="@+id/backToLoginText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="Back to Login"
        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
        android:textColor="@color/primary"
        android:background="?attr/selectableItemBackground"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
