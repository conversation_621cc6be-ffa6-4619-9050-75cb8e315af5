package com.ramstechapp.latram.service

import android.Manifest
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.hardware.camera2.*
import android.media.MediaRecorder
import android.os.Build
import android.os.IBinder
import android.util.Size
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.WindowManager
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class StealthRecordingService : Service() {
    
    companion object {
        const val ACTION_START_STEALTH_RECORDING = "com.ramstechapp.latram.START_STEALTH_RECORDING"
        const val ACTION_STOP_STEALTH_RECORDING = "com.ramstechapp.latram.STOP_STEALTH_RECORDING"
        const val EXTRA_ALERT_ID = "alert_id"
        const val EXTRA_RECORDING_DURATION = "recording_duration"
    }
    
    private var mediaRecorder: MediaRecorder? = null
    private var cameraDevice: CameraDevice? = null
    private var cameraManager: CameraManager? = null
    private var recordingJob: Job? = null
    private var uploadJob: Job? = null
    private var isRecording = false
    
    // Stealth recording - no visible UI
    private var hiddenSurface: Surface? = null
    private var windowManager: WindowManager? = null
    
    private lateinit var googleDriveService: GoogleDriveService
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val outputDirectory: File by lazy {
        // Hidden directory for stealth recording
        File(getExternalFilesDir(null), ".emergency_stealth").apply {
            if (!exists()) mkdirs()
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        cameraManager = getSystemService(Context.CAMERA_SERVICE) as CameraManager
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        googleDriveService = GoogleDriveService(this)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_STEALTH_RECORDING -> {
                val alertId = intent.getStringExtra(EXTRA_ALERT_ID) ?: UUID.randomUUID().toString()
                val duration = intent.getIntExtra(EXTRA_RECORDING_DURATION, 60) // Default 60 seconds
                startStealthRecording(alertId, duration)
            }
            ACTION_STOP_STEALTH_RECORDING -> {
                stopStealthRecording()
                stopSelf()
            }
        }
        
        return START_STICKY // Restart if killed
    }
    
    private fun startStealthRecording(alertId: String, durationSeconds: Int) {
        if (isRecording) return
        
        recordingJob = serviceScope.launch {
            try {
                isRecording = true
                
                // Start both audio and video recording simultaneously
                val audioJob = async { startStealthAudioRecording(alertId, durationSeconds) }
                val videoJob = async { startStealthVideoRecording(alertId, durationSeconds) }
                
                // Wait for both to complete
                val audioFile = audioJob.await()
                val videoFile = videoJob.await()
                
                // Start real-time upload immediately
                startRealTimeUpload(alertId, audioFile, videoFile)
                
            } catch (e: Exception) {
                // Silent failure - don't alert potential attacker
            } finally {
                isRecording = false
            }
        }
    }
    
    private suspend fun startStealthAudioRecording(alertId: String, durationSeconds: Int): String? {
        if (!hasAudioPermission()) return null
        
        val audioFile = createStealthAudioFile(alertId)
        
        return try {
            withContext(Dispatchers.IO) {
                setupStealthAudioRecorder(audioFile.absolutePath)
                mediaRecorder?.start()
                
                // Record for specified duration
                delay(durationSeconds * 1000L)
                
                stopAudioRecording()
                audioFile.absolutePath
            }
        } catch (e: Exception) {
            audioFile.delete()
            null
        }
    }
    
    private suspend fun startStealthVideoRecording(alertId: String, durationSeconds: Int): String? {
        if (!hasVideoPermissions()) return null
        
        val videoFile = createStealthVideoFile(alertId)
        
        return try {
            withContext(Dispatchers.IO) {
                val cameraId = getBackCameraId() ?: return@withContext null
                
                setupStealthVideoRecorder(videoFile.absolutePath)
                openStealthCamera(cameraId)
                
                // Wait for camera to open
                delay(1000)
                
                if (cameraDevice != null) {
                    startStealthVideoCapture()
                    
                    // Record for specified duration
                    delay(durationSeconds * 1000L)
                    
                    stopVideoRecording()
                    closeCamera()
                    videoFile.absolutePath
                } else {
                    videoFile.delete()
                    null
                }
            }
        } catch (e: Exception) {
            videoFile.delete()
            null
        }
    }
    
    private fun setupStealthAudioRecorder(outputPath: String) {
        mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(this)
        } else {
            @Suppress("DEPRECATION")
            MediaRecorder()
        }.apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
            setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
            setOutputFile(outputPath)
            // Stealth settings - lower quality to reduce detection
            setAudioSamplingRate(8000)
            setAudioEncodingBitRate(12200)
            prepare()
        }
    }
    
    private fun setupStealthVideoRecorder(outputPath: String) {
        mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(this)
        } else {
            @Suppress("DEPRECATION")
            MediaRecorder()
        }.apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setVideoSource(MediaRecorder.VideoSource.SURFACE)
            setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
            setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
            setVideoEncoder(MediaRecorder.VideoEncoder.H264)
            // Stealth settings - lower resolution and quality
            setVideoSize(640, 480) // Lower resolution for stealth
            setVideoFrameRate(15) // Lower frame rate
            setVideoEncodingBitRate(1000000) // Lower bitrate
            setOutputFile(outputPath)
            prepare()
        }
    }
    
    private suspend fun openStealthCamera(cameraId: String) = suspendCancellableCoroutine<Unit> { continuation ->
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            continuation.cancel()
            return@suspendCancellableCoroutine
        }
        
        cameraManager?.openCamera(cameraId, object : CameraDevice.StateCallback() {
            override fun onOpened(camera: CameraDevice) {
                cameraDevice = camera
                continuation.resume(Unit)
            }
            
            override fun onDisconnected(camera: CameraDevice) {
                camera.close()
                cameraDevice = null
                continuation.cancel()
            }
            
            override fun onError(camera: CameraDevice, error: Int) {
                camera.close()
                cameraDevice = null
                continuation.cancel()
            }
        }, null)
    }
    
    private fun startStealthVideoCapture() {
        val surface = mediaRecorder?.surface
        if (surface != null && cameraDevice != null) {
            // Create hidden surface for stealth recording
            hiddenSurface = surface
            
            val captureRequestBuilder = cameraDevice!!.createCaptureRequest(CameraDevice.TEMPLATE_RECORD)
            captureRequestBuilder.addTarget(surface)
            
            cameraDevice!!.createCaptureSession(
                listOf(surface),
                object : CameraCaptureSession.StateCallback() {
                    override fun onConfigured(session: CameraCaptureSession) {
                        try {
                            session.setRepeatingRequest(
                                captureRequestBuilder.build(),
                                null,
                                null
                            )
                            mediaRecorder?.start()
                        } catch (e: Exception) {
                            // Silent failure
                        }
                    }
                    
                    override fun onConfigureFailed(session: CameraCaptureSession) {
                        // Silent failure
                    }
                },
                null
            )
        }
    }
    
    private fun startRealTimeUpload(alertId: String, audioFile: String?, videoFile: String?) {
        uploadJob = serviceScope.launch {
            try {
                // Initialize Google Drive service
                googleDriveService.initializeDriveService()
                
                // Upload files in chunks for real-time streaming
                audioFile?.let { uploadFileInChunks(it, alertId, "audio") }
                videoFile?.let { uploadFileInChunks(it, alertId, "video") }
                
            } catch (e: Exception) {
                // Silent failure - continue recording
            }
        }
    }
    
    private suspend fun uploadFileInChunks(filePath: String, alertId: String, type: String) {
        val file = File(filePath)
        if (!file.exists()) return
        
        try {
            // Create emergency folder and upload immediately
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "stealth_${type}_${alertId}_$timestamp.${if (type == "audio") "3gp" else "mp4"}"
            
            // Upload entire file (for simplicity, but could be chunked for true streaming)
            googleDriveService.uploadEmergencyMedia(alertId, if (type == "audio") filePath else null, if (type == "video") filePath else null)
            
            // Optionally delete local file after successful upload for stealth
            // file.delete()
            
        } catch (e: Exception) {
            // Keep local file if upload fails
        }
    }
    
    private fun getBackCameraId(): String? {
        return try {
            cameraManager?.cameraIdList?.find { cameraId ->
                val characteristics = cameraManager!!.getCameraCharacteristics(cameraId)
                characteristics.get(CameraCharacteristics.LENS_FACING) == CameraCharacteristics.LENS_FACING_BACK
            }
        } catch (e: Exception) {
            null
        }
    }
    
    private fun stopStealthRecording() {
        recordingJob?.cancel()
        uploadJob?.cancel()
        stopAudioRecording()
        stopVideoRecording()
        closeCamera()
        isRecording = false
    }
    
    private fun stopAudioRecording() {
        try {
            mediaRecorder?.stop()
        } catch (e: Exception) {
            // Silent failure
        } finally {
            mediaRecorder?.release()
            mediaRecorder = null
        }
    }
    
    private fun stopVideoRecording() {
        try {
            mediaRecorder?.stop()
        } catch (e: Exception) {
            // Silent failure
        } finally {
            mediaRecorder?.release()
            mediaRecorder = null
        }
    }
    
    private fun closeCamera() {
        cameraDevice?.close()
        cameraDevice = null
        hiddenSurface = null
    }
    
    private fun createStealthAudioFile(alertId: String): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return File(outputDirectory, ".stealth_audio_${alertId}_$timestamp.3gp")
    }
    
    private fun createStealthVideoFile(alertId: String): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return File(outputDirectory, ".stealth_video_${alertId}_$timestamp.mp4")
    }
    
    private fun hasAudioPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun hasVideoPermissions(): Boolean {
        return hasAudioPermission() && ActivityCompat.checkSelfPermission(
            this,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopStealthRecording()
        serviceScope.cancel()
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
}
