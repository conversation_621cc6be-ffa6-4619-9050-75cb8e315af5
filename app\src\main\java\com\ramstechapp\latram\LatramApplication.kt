package com.ramstechapp.latram

import android.app.Application
import com.ramstechapp.latram.data.database.LatramDatabase
import com.ramstechapp.latram.data.remote.api.SupabaseApiService
import com.ramstechapp.latram.data.remote.SupabaseConfig
import com.ramstechapp.latram.repository.EmergencyRepository
import com.ramstechapp.latram.service.*
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

class LatramApplication : Application() {
    
    // Database
    val database by lazy { LatramDatabase.getDatabase(this) }
    
    // API Service
    val supabaseApi by lazy { createSupabaseApiService() }
    
    // Repository
    val emergencyRepository by lazy { EmergencyRepository(database, supabaseApi) }
    
    // Services
    val locationService by lazy { LocationService(this) }
    val smsService by lazy { SmsService(this) }
    val callService by lazy { CallService(this) }
    val mediaRecordingService by lazy { MediaRecordingService(this) }
    val googleDriveService by lazy { GoogleDriveService(this) }
    
    override fun onCreate() {
        super.onCreate()
        initializeDefaultSettings()
    }
    
    private fun createSupabaseApiService(): SupabaseApiService {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
        
        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
        
        val retrofit = Retrofit.Builder()
            .baseUrl(SupabaseConfig.SUPABASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        return retrofit.create(SupabaseApiService::class.java)
    }
    
    private fun initializeDefaultSettings() {
        // Initialize default settings in background
        Thread {
            try {
                val existingSettings = database.userSettingsDao().getSettingsSync()
                if (existingSettings == null) {
                    val defaultSettings = com.ramstechapp.latram.data.model.UserSettings()
                    database.userSettingsDao().insertSettings(defaultSettings)
                }
            } catch (e: Exception) {
                // Handle initialization error
            }
        }.start()
    }
}
