package com.ramstechapp.latram.service

import android.Manifest
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.telephony.SmsManager
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.suspendCancellableCoroutine
import java.text.SimpleDateFormat
import java.util.*
import kotlin.coroutines.resume

class SmsService(private val context: Context) {
    
    companion object {
        const val SMS_SENT_ACTION = "com.ramstechapp.latram.SMS_SENT"
        const val SMS_DELIVERED_ACTION = "com.ramstechapp.latram.SMS_DELIVERED"
        private const val MAX_SMS_LENGTH = 160
    }
    
    private val smsManager = SmsManager.getDefault()
    
    suspend fun sendEmergencySMS(
        phoneNumber: String,
        message: String,
        location: String,
        dateTime: Date = Date()
    ): Boolean = suspendCancellableCoroutine { continuation ->
        
        if (!hasSmsPermission()) {
            continuation.resume(false)
            return@suspendCancellableCoroutine
        }
        
        val formattedMessage = formatEmergencyMessage(message, location, dateTime)
        
        // Create pending intents for SMS status
        val sentIntent = PendingIntent.getBroadcast(
            context,
            0,
            Intent(SMS_SENT_ACTION),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val deliveredIntent = PendingIntent.getBroadcast(
            context,
            0,
            Intent(SMS_DELIVERED_ACTION),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // Register broadcast receivers for SMS status
        val sentReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (resultCode) {
                    android.app.Activity.RESULT_OK -> {
                        continuation.resume(true)
                    }
                    else -> {
                        continuation.resume(false)
                    }
                }
                context?.unregisterReceiver(this)
            }
        }
        
        val deliveredReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                // SMS was delivered successfully
                // This can be used for additional logging if needed
                context?.unregisterReceiver(this)
            }
        }
        
        // Register receivers
        context.registerReceiver(sentReceiver, IntentFilter(SMS_SENT_ACTION))
        context.registerReceiver(deliveredReceiver, IntentFilter(SMS_DELIVERED_ACTION))
        
        try {
            // Check if message needs to be split
            if (formattedMessage.length > MAX_SMS_LENGTH) {
                val parts = smsManager.divideMessage(formattedMessage)
                val sentIntents = ArrayList<PendingIntent>()
                val deliveredIntents = ArrayList<PendingIntent>()
                
                repeat(parts.size) {
                    sentIntents.add(sentIntent)
                    deliveredIntents.add(deliveredIntent)
                }
                
                smsManager.sendMultipartTextMessage(
                    phoneNumber,
                    null,
                    parts,
                    sentIntents,
                    deliveredIntents
                )
            } else {
                smsManager.sendTextMessage(
                    phoneNumber,
                    null,
                    formattedMessage,
                    sentIntent,
                    deliveredIntent
                )
            }
        } catch (e: Exception) {
            context.unregisterReceiver(sentReceiver)
            context.unregisterReceiver(deliveredReceiver)
            continuation.resume(false)
        }
        
        continuation.invokeOnCancellation {
            try {
                context.unregisterReceiver(sentReceiver)
                context.unregisterReceiver(deliveredReceiver)
            } catch (e: Exception) {
                // Receivers might already be unregistered
            }
        }
    }
    
    private fun formatEmergencyMessage(
        message: String,
        location: String,
        dateTime: Date
    ): String {
        val dateFormatter = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
        val formattedDate = dateFormatter.format(dateTime)
        
        return buildString {
            append("🚨 EMERGENCY ALERT 🚨\n")
            append("$message\n")
            append("Time: $formattedDate\n")
            append("$location\n")
            append("Sent via LATRAM Emergency App")
        }
    }
    
    fun validatePhoneNumber(phoneNumber: String): Boolean {
        // Basic phone number validation
        val cleanNumber = phoneNumber.replace(Regex("[^+\\d]"), "")
        return cleanNumber.length >= 10 && cleanNumber.matches(Regex("^\\+?[1-9]\\d{1,14}$"))
    }
    
    fun formatPhoneNumber(phoneNumber: String): String {
        // Remove all non-digit characters except +
        return phoneNumber.replace(Regex("[^+\\d]"), "")
    }
    
    private fun hasSmsPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.SEND_SMS
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    fun canSendSms(): Boolean {
        return hasSmsPermission() && smsManager != null
    }
}
