package com.ramstechapp.latram.data.database

import androidx.room.TypeConverter
import com.ramstechapp.latram.data.model.EmergencyType
import com.ramstechapp.latram.data.model.LocationAccuracy
import java.util.Date

class Converters {
    
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
    
    @TypeConverter
    fun fromEmergencyType(type: EmergencyType): String {
        return type.name
    }
    
    @TypeConverter
    fun toEmergencyType(type: String): EmergencyType {
        return EmergencyType.valueOf(type)
    }
    
    @TypeConverter
    fun fromLocationAccuracy(accuracy: LocationAccuracy): String {
        return accuracy.name
    }
    
    @TypeConverter
    fun toLocationAccuracy(accuracy: String): LocationAccuracy {
        return LocationAccuracy.valueOf(accuracy)
    }
}
