package com.ramstechapp.latram.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ramstechapp.latram.LatramApplication
import com.ramstechapp.latram.data.model.EmergencyType
import com.ramstechapp.latram.utils.Event
import kotlinx.coroutines.launch

class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository = (application as LatramApplication).emergencyRepository
    
    private val _emergencyTriggerEvent = MutableLiveData<Event<Pair<String, EmergencyType>>>()
    val emergencyTriggerEvent: LiveData<Event<Pair<String, EmergencyType>>> = _emergencyTriggerEvent
    
    private val _showToast = MutableLiveData<Event<String>>()
    val showToast: LiveData<Event<String>> = _showToast
    
    private val _permissionsGranted = MutableLiveData<Boolean>()
    val permissionsGranted: LiveData<Boolean> = _permissionsGranted
    
    private val _isEmergencyActive = MutableLiveData<Boolean>()
    val isEmergencyActive: LiveData<Boolean> = _isEmergencyActive
    
    init {
        _isEmergencyActive.value = false
        _permissionsGranted.value = false
    }
    
    fun triggerEmergency(message: String = "", emergencyType: EmergencyType = EmergencyType.GENERAL) {
        viewModelScope.launch {
            try {
                // Check if we have emergency contacts
                val contactsCount = repository.getActiveContactsCount()
                if (contactsCount == 0) {
                    _showToast.value = Event("Please add an emergency contact first")
                    return@launch
                }
                
                // Get default message if none provided
                val finalMessage = if (message.isBlank()) {
                    val settings = repository.getSettingsSync()
                    settings?.defaultMessage ?: "Emergency! I need help."
                } else {
                    message
                }
                
                _isEmergencyActive.value = true
                _emergencyTriggerEvent.value = Event(Pair(finalMessage, emergencyType))
                
            } catch (e: Exception) {
                _showToast.value = Event("Failed to trigger emergency: ${e.message}")
                _isEmergencyActive.value = false
            }
        }
    }
    
    fun cancelEmergency() {
        _isEmergencyActive.value = false
        _showToast.value = Event("Emergency cancelled")
    }
    
    fun onPermissionsGranted() {
        _permissionsGranted.value = true
    }
    
    fun onPermissionsPartiallyGranted() {
        _permissionsGranted.value = false
        _showToast.value = Event("Some features may not work without all permissions")
    }
    
    fun onEmergencyCompleted() {
        _isEmergencyActive.value = false
    }
}
