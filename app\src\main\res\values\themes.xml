<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.LATRAM" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorPrimaryContainer">@color/primary_container</item>
        <item name="colorOnPrimaryContainer">@color/on_primary_container</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSecondaryContainer">@color/secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/on_secondary_container</item>
        
        <!-- Tertiary brand color. -->
        <item name="colorTertiary">@color/tertiary</item>
        <item name="colorOnTertiary">@color/on_tertiary</item>
        <item name="colorTertiaryContainer">@color/tertiary_container</item>
        <item name="colorOnTertiaryContainer">@color/on_tertiary_container</item>
        
        <!-- Error colors. -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/on_error</item>
        <item name="colorErrorContainer">@color/error_container</item>
        <item name="colorOnErrorContainer">@color/on_error_container</item>
        
        <!-- Surface colors. -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        
        <!-- Outline colors. -->
        <item name="colorOutline">@color/outline</item>
        <item name="colorOutlineVariant">@color/outline_variant</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <!-- Customize your theme here. -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <!-- Emergency Button Style -->
    <style name="EmergencyButtonStyle" parent="Widget.Material3.FloatingActionButton.Large">
        <item name="backgroundTint">@color/emergency_red</item>
        <item name="tint">@color/white</item>
        <item name="elevation">8dp</item>
        <item name="pressedTranslationZ">12dp</item>
    </style>
    
    <!-- Card Styles -->
    <style name="EmergencyCardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>
    
    <!-- Text Styles -->
    <style name="EmergencyTitleStyle" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/emergency_red</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <style name="StatusTextStyle" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/on_surface</item>
    </style>
    
    <!-- Button Styles -->
    <style name="PrimaryButtonStyle" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/on_primary</item>
    </style>
    
    <style name="SecondaryButtonStyle" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/primary</item>
        <item name="android:textColor">@color/primary</item>
    </style>
    
    <!-- Input Field Styles -->
    <style name="InputFieldStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/on_surface_variant</item>
    </style>
</resources>
