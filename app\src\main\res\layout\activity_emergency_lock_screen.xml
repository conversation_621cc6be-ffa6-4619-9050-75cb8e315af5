<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/emergency_background"
    android:padding="24dp">

    <!-- App Title -->
    <TextView
        android:id="@+id/appTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="LATRAM"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineLarge"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/appSubtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Emergency Alert System"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="@android:color/white"
        android:alpha="0.8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appTitle" />

    <!-- Status Text -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:text="Emergency Quick Access Ready"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
        android:textColor="@android:color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appSubtitle" />

    <!-- Main Emergency Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/emergencyButton"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="48dp"
        android:contentDescription="@string/cd_emergency_button"
        android:src="@drawable/ic_emergency"
        android:text="EMERGENCY"
        app:backgroundTint="@color/emergency_red"
        app:fabSize="auto"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusText"
        app:tint="@android:color/white" />

    <!-- Countdown Text -->
    <TextView
        android:id="@+id/countdownText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="Press for emergency alert"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="@android:color/white"
        android:alpha="0.9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emergencyButton" />

    <!-- Quick Emergency Type Buttons -->
    <LinearLayout
        android:id="@+id/quickButtonsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/countdownText">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/medicalButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="MEDICAL"
            android:textColor="@android:color/white"
            app:strokeColor="@android:color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/fireButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:text="FIRE"
            android:textColor="@android:color/white"
            app:strokeColor="@android:color/white" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/quickButtonsLayout2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/quickButtonsLayout">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/policeButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="POLICE"
            android:textColor="@android:color/white"
            app:strokeColor="@android:color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/accidentButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="ACCIDENT"
            android:textColor="@android:color/white"
            app:strokeColor="@android:color/white" />

    </LinearLayout>

    <!-- Bottom Action Buttons -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancelButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="CLOSE"
            android:textColor="@android:color/white"
            app:strokeColor="@android:color/white" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/openAppButton"
            style="@style/Widget.Material3.Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="OPEN APP"
            android:textColor="@color/emergency_red"
            app:backgroundTint="@android:color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
