<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <!-- Contact Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/contactName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Contact Name"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="@color/on_surface" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/primaryIndicator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="PRIMARY"
                        android:textSize="10sp"
                        app:chipBackgroundColor="@color/primary"
                        app:chipMinHeight="24dp"
                        android:textColor="@color/on_primary"
                        android:visibility="gone" />

                </LinearLayout>

                <TextView
                    android:id="@+id/contactPhone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="+1234567890"
                    android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                    android:textColor="@color/on_surface_variant" />

                <TextView
                    android:id="@+id/contactRelationship"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="Relationship"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="@color/on_surface_variant" />

            </LinearLayout>

            <!-- Delete Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/deleteButton"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                app:icon="@drawable/ic_delete"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconTint="@color/error" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/callButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Call"
                app:icon="@drawable/ic_call" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/smsButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="SMS"
                app:icon="@drawable/ic_sms" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/setPrimaryButton"
                style="@style/Widget.Material3.Button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Set Primary"
                app:icon="@drawable/ic_star" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
