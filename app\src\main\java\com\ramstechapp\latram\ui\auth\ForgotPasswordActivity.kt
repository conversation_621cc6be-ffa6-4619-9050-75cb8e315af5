package com.ramstechapp.latram.ui.auth

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.ramstechapp.latram.databinding.ActivityForgotPasswordBinding

class ForgotPasswordActivity : AppCompatActivity() {

    private lateinit var binding: ActivityForgotPasswordBinding
    private lateinit var authViewModel: AuthViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityForgotPasswordBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        authViewModel = ViewModelProvider(this)[AuthViewModel::class.java]
        
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupClickListeners() {
        binding.apply {
            sendResetButton.setOnClickListener {
                sendPasswordReset()
            }
            
            backToLoginText.setOnClickListener {
                startActivity(Intent(this@ForgotPasswordActivity, LoginActivity::class.java))
                finish()
            }
            
            backButton.setOnClickListener {
                finish()
            }
        }
    }
    
    private fun observeViewModel() {
        authViewModel.forgotPasswordState.observe(this) { state ->
            when (state) {
                is ForgotPasswordState.Loading -> {
                    showLoading(true)
                }
                is ForgotPasswordState.Success -> {
                    showLoading(false)
                    showSuccessMessage()
                }
                is ForgotPasswordState.Error -> {
                    showLoading(false)
                    Toast.makeText(this, state.message, Toast.LENGTH_LONG).show()
                }
                is ForgotPasswordState.Idle -> {
                    showLoading(false)
                }
            }
        }
    }
    
    private fun sendPasswordReset() {
        val email = binding.emailInput.text.toString().trim()
        
        if (!validateEmail(email)) {
            return
        }
        
        authViewModel.forgotPassword(email)
    }
    
    private fun validateEmail(email: String): Boolean {
        if (email.isEmpty()) {
            binding.emailInputLayout.error = "Email is required"
            return false
        }
        
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.emailInputLayout.error = "Please enter a valid email"
            return false
        }
        
        binding.emailInputLayout.error = null
        return true
    }
    
    private fun showLoading(show: Boolean) {
        binding.apply {
            progressBar.visibility = if (show) View.VISIBLE else View.GONE
            sendResetButton.isEnabled = !show
            emailInput.isEnabled = !show
        }
    }
    
    private fun showSuccessMessage() {
        binding.apply {
            // Hide the form
            emailInputLayout.visibility = View.GONE
            sendResetButton.visibility = View.GONE
            
            // Show success message
            successLayout.visibility = View.VISIBLE
            successMessage.text = "Password reset instructions have been sent to ${emailInput.text}"
            
            // Update back button text
            backToLoginText.text = "Back to Login"
        }
    }
}
