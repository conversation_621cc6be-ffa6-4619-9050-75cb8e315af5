# LATRAM Stealth Recording Features

## Overview

LATRAM includes advanced stealth recording capabilities designed to capture evidence during emergency situations while remaining completely hidden from potential attackers. These features ensure that critical evidence is preserved even if the device is compromised.

## 🕵️ Stealth Recording Capabilities

### 1. **Covert Audio/Video Recording**
- Records without any visible indicators (no recording icons, sounds, or notifications)
- Uses background camera and microphone access
- Minimal resource usage to avoid detection
- Configurable quality settings (lower quality = more stealth)

### 2. **Real-time Cloud Upload**
- Streams recordings directly to Google Drive during capture
- Files are uploaded in chunks as they're recorded
- Ensures evidence survives even if device is destroyed
- Uses hidden folder structure in Google Drive

### 3. **Hidden Local Storage**
- Files stored in hidden directories (`.emergency_stealth`)
- Encrypted file names with random identifiers
- Files marked as hidden from normal file browsers
- Optional auto-deletion after successful upload

### 4. **Silent Operation**
- Disables camera shutter sounds
- Mutes system recording indicators
- No visible UI during recording
- Minimal vibration feedback (optional)

## 🔧 Technical Implementation

### Stealth Recording Service
```kotlin
// Start stealth recording
val intent = Intent(context, StealthRecordingService::class.java).apply {
    action = StealthRecordingService.ACTION_START_STEALTH_RECORDING
    putExtra(StealthRecordingService.EXTRA_ALERT_ID, alertId)
    putExtra(StealthRecordingService.EXTRA_RECORDING_DURATION, 60)
}
context.startService(intent)
```

### Key Components
- **StealthRecordingService**: Background service for covert recording
- **StealthManager**: Configuration and settings management
- **StealthIndicator**: Minimal visual feedback (optional)
- **Enhanced GoogleDriveService**: Real-time upload capabilities

## 📁 File Organization

### Local Storage Structure
```
/Android/data/com.ramstechapp.latram/files/.emergency_stealth/
├── .stealth_audio_[alertId]_[timestamp].3gp
├── .stealth_video_[alertId]_[timestamp].mp4
└── .upload_queue/
    ├── pending_uploads.json
    └── failed_uploads.json
```

### Google Drive Structure
```
Google Drive/
└── .LATRAM_Emergency_Stealth/ (hidden folder)
    └── Alert_[timestamp]_[alertId]/
        ├── stealth_audio_[alertId]_[timestamp].3gp
        ├── stealth_video_[alertId]_[timestamp].mp4
        └── metadata.json
```

## ⚙️ Configuration Options

### Stealth Quality Settings
- **LOW**: Minimal quality, maximum stealth (8kHz audio, 480p video)
- **MEDIUM**: Balanced quality and stealth (16kHz audio, 720p video)
- **HIGH**: Best quality, less stealthy (44kHz audio, 1080p video)

### Recording Options
- **Silent Recording**: Disable all sounds and indicators
- **Background Recording**: Continue even when app is closed
- **Real-time Upload**: Stream to cloud during recording
- **Auto-delete**: Remove local files after upload
- **Recording Duration**: 30-300 seconds

## 🛡️ Security Features

### Evidence Preservation
1. **Immediate Upload**: Files start uploading within seconds of recording start
2. **Redundant Storage**: Both local and cloud copies maintained
3. **Hidden Folders**: Files stored in hidden directories
4. **Encrypted Names**: File names use random identifiers
5. **Metadata Protection**: Recording details stored separately

### Anti-Detection Measures
1. **No Visual Indicators**: No recording icons or notifications
2. **Silent Operation**: Camera sounds disabled
3. **Background Processing**: Runs as system service
4. **Minimal Resource Usage**: Low CPU/battery impact
5. **Stealth Timing**: Records during normal emergency workflow

## 🚀 Usage Instructions

### Automatic Activation
Stealth recording automatically starts when:
1. Emergency alert is triggered
2. Stealth recording is enabled in settings
3. Required permissions are granted
4. Google Drive is connected

### Manual Configuration
1. Open LATRAM Settings
2. Navigate to "Stealth Recording" section
3. Enable desired stealth features:
   - ✅ Stealth Recording
   - ✅ Real-time Upload
   - ✅ Silent Recording
   - ⚠️ Auto-delete (optional)
4. Configure quality settings
5. Test Google Drive connection

### Verification
To verify stealth recording is working:
1. Trigger a test emergency alert
2. Check Google Drive for new hidden folder
3. Verify recordings are uploaded
4. Confirm no visible indicators appeared

## 🔒 Privacy and Legal Considerations

### Important Notes
- **Legal Compliance**: Ensure recording is legal in your jurisdiction
- **Consent**: Be aware of consent requirements for recording
- **Privacy**: Stealth features are for emergency use only
- **Data Security**: Recordings contain sensitive information

### Recommended Settings
- Enable stealth recording only for genuine emergencies
- Use auto-delete to minimize local storage
- Regularly review and clean up cloud storage
- Inform trusted contacts about stealth capabilities

## 🛠️ Troubleshooting

### Common Issues

**Recording Not Starting**
- Check camera/microphone permissions
- Verify Google Drive connection
- Ensure sufficient storage space
- Check battery optimization settings

**Upload Failures**
- Verify internet connection
- Check Google Drive storage quota
- Confirm Google account permissions
- Review network restrictions

**Detection by Attackers**
- Use lowest quality settings
- Enable silent recording
- Disable visual indicators
- Use auto-delete feature

### Debug Information
Stealth recording logs are minimal for security. Check:
1. Emergency alert history for recording status
2. Google Drive for uploaded files
3. Device storage for local files
4. Network logs for upload activity

## 🔧 Advanced Configuration

### Developer Settings
For advanced users, additional configuration options:

```kotlin
val stealthManager = StealthManager(context)
stealthManager.apply {
    setStealthQuality(StealthQuality.LOW)
    setSilentRecording(true)
    setRealTimeUploadEnabled(true)
    setAutoDeleteAfterUpload(true)
}
```

### Custom Upload Intervals
```kotlin
// Upload in smaller chunks for real-time streaming
googleDriveService.streamUploadFile(
    filePath = audioFile,
    fileName = "stealth_audio_${alertId}.3gp",
    mimeType = "audio/3gpp",
    alertId = alertId
) { progress ->
    // Optional progress callback
}
```

## 📞 Support

For issues with stealth recording features:
1. Check device compatibility
2. Verify all permissions granted
3. Test Google Drive connectivity
4. Review emergency alert logs
5. Contact support with specific error details

**Remember**: Stealth features are designed for emergency situations where evidence preservation is critical. Use responsibly and in accordance with local laws.
