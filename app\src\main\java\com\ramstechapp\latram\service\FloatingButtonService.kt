package com.ramstechapp.latram.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.view.*
import android.widget.ImageView
import androidx.core.app.NotificationCompat
import com.ramstechapp.latram.R
import com.ramstechapp.latram.ui.MainActivity
import com.ramstechapp.latram.ui.emergency.EmergencyLockScreenActivity

class FloatingButtonService : Service() {
    
    companion object {
        const val ACTION_START_FLOATING_BUTTON = "com.ramstechapp.latram.START_FLOATING_BUTTON"
        const val ACTION_STOP_FLOATING_BUTTON = "com.ramstechapp.latram.STOP_FLOATING_BUTTON"
        const val ACTION_SHOW_EMERGENCY_ACCESS = "com.ramstechapp.latram.SHOW_EMERGENCY_ACCESS"
        const val NOTIFICATION_ID = 2001
        const val CHANNEL_ID = "floating_button_channel"
    }
    
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var emergencyButton: ImageView? = null
    private var isFloatingButtonVisible = false
    
    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_FLOATING_BUTTON -> {
                if (canDrawOverlays()) {
                    showFloatingButton()
                    startForeground(NOTIFICATION_ID, createNotification())
                } else {
                    stopSelf()
                }
            }
            ACTION_STOP_FLOATING_BUTTON -> {
                hideFloatingButton()
                stopSelf()
            }
            ACTION_SHOW_EMERGENCY_ACCESS -> {
                showEmergencyAccess()
            }
        }
        
        return START_STICKY // Restart if killed
    }
    
    private fun showFloatingButton() {
        if (isFloatingButtonVisible || !canDrawOverlays()) return
        
        try {
            // Create floating button layout
            floatingView = LayoutInflater.from(this).inflate(R.layout.floating_emergency_button, null)
            emergencyButton = floatingView?.findViewById(R.id.floatingEmergencyButton)
            
            // Set up window parameters
            val layoutParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.END
                x = 20 // Distance from right edge
                y = 200 // Distance from top
            }
            
            // Set up touch listeners
            setupTouchListeners(layoutParams)
            
            // Add to window manager
            windowManager?.addView(floatingView, layoutParams)
            isFloatingButtonVisible = true
            
        } catch (e: Exception) {
            // Handle permission or other errors
            stopSelf()
        }
    }
    
    private fun setupTouchListeners(layoutParams: WindowManager.LayoutParams) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f
        var isMoving = false
        
        emergencyButton?.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = layoutParams.x
                    initialY = layoutParams.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isMoving = false
                    true
                }
                
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = (event.rawX - initialTouchX).toInt()
                    val deltaY = (event.rawY - initialTouchY).toInt()
                    
                    // Check if user is trying to move the button
                    if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
                        isMoving = true
                        layoutParams.x = initialX - deltaX
                        layoutParams.y = initialY + deltaY
                        windowManager?.updateViewLayout(floatingView, layoutParams)
                    }
                    true
                }
                
                MotionEvent.ACTION_UP -> {
                    if (!isMoving) {
                        // Button was tapped, not moved
                        onEmergencyButtonClicked()
                    }
                    true
                }
                
                else -> false
            }
        }
    }
    
    private fun onEmergencyButtonClicked() {
        // Vibrate for feedback
        val vibrator = getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            vibrator.vibrate(android.os.VibrationEffect.createOneShot(100, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(100)
        }
        
        // Show emergency access screen
        showEmergencyAccess()
    }
    
    private fun showEmergencyAccess() {
        val intent = Intent(this, EmergencyLockScreenActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_SINGLE_TOP
        }
        startActivity(intent)
    }
    
    private fun hideFloatingButton() {
        if (isFloatingButtonVisible && floatingView != null) {
            try {
                windowManager?.removeView(floatingView)
                isFloatingButtonVisible = false
                floatingView = null
                emergencyButton = null
            } catch (e: Exception) {
                // View might already be removed
            }
        }
    }
    
    private fun canDrawOverlays(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Emergency Quick Access",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows emergency button overlay for quick access"
                setShowBadge(false)
                enableVibration(false)
                setSound(null, null)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val stopIntent = Intent(this, FloatingButtonService::class.java).apply {
            action = ACTION_STOP_FLOATING_BUTTON
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val mainIntent = Intent(this, MainActivity::class.java)
        val mainPendingIntent = PendingIntent.getActivity(
            this, 0, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Emergency Quick Access Active")
            .setContentText("Tap the floating button for emergency access")
            .setSmallIcon(R.drawable.ic_emergency)
            .setContentIntent(mainPendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .addAction(
                R.drawable.ic_cancel,
                "Disable",
                stopPendingIntent
            )
            .build()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        hideFloatingButton()
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
}
