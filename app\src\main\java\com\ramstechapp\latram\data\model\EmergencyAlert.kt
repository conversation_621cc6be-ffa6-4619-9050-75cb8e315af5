package com.ramstechapp.latram.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
@Entity(tableName = "emergency_alerts")
data class EmergencyAlert(
    @PrimaryKey
    val id: String,
    val userId: String? = null,
    val location: String, // GPS coordinates or map URL
    val latitude: Double,
    val longitude: Double,
    val message: String,
    val dateTime: Date,
    val mediaLink: String? = null,
    val contactCalled: String,
    val smsDelivered: Boolean = false,
    val callMade: Boolean = false,
    val mediaUploaded: Boolean = false,
    val syncedToServer: Boolean = false,
    val emergencyType: EmergencyType = EmergencyType.GENERAL,
    val audioFilePath: String? = null,
    val videoFilePath: String? = null,
    val driveFileId: String? = null
) : Parcelable

enum class EmergencyType {
    GENERAL,
    MEDICAL,
    FIRE,
    POLICE,
    ACCIDENT,
    NATURAL_DISASTER
}

@Parcelize
@Entity(tableName = "emergency_contacts")
data class EmergencyContact(
    @PrimaryKey
    val id: String,
    val name: String,
    val phoneNumber: String,
    val relationship: String,
    val isPrimary: Boolean = false,
    val isActive: Boolean = true,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) : Parcelable

@Parcelize
@Entity(tableName = "user_settings")
data class UserSettings(
    @PrimaryKey
    val id: String = "default",
    val defaultMessage: String = "Emergency! I need help. Please check my location.",
    val autoCallEnabled: Boolean = true,
    val autoRecordEnabled: Boolean = true,
    val recordingDuration: Int = 30, // seconds
    val googleDriveEnabled: Boolean = true,
    val ttsEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,
    val locationAccuracy: LocationAccuracy = LocationAccuracy.HIGH,
    val emergencyTimeout: Int = 300, // seconds before auto-cancel
    val lastBackupDate: Date? = null
) : Parcelable

enum class LocationAccuracy {
    HIGH,
    MEDIUM,
    LOW
}
