package com.ramstechapp.latram.service

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.hardware.camera2.*
import android.media.MediaRecorder
import android.os.Build
import android.util.Size
import android.view.Surface
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.*
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

class MediaRecordingService(private val context: Context) {
    
    private var mediaRecorder: MediaRecorder? = null
    private var cameraDevice: CameraDevice? = null
    private var cameraManager: CameraManager? = null
    private var recordingJob: Job? = null
    
    private val outputDirectory: File by lazy {
        File(context.getExternalFilesDir(null), "emergency_media").apply {
            if (!exists()) mkdirs()
        }
    }
    
    init {
        cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    }
    
    suspend fun recordAudio(durationSeconds: Int = 30): String? {
        if (!hasAudioPermission()) {
            return null
        }
        
        val audioFile = createAudioFile()
        
        return try {
            withContext(Dispatchers.IO) {
                setupAudioRecorder(audioFile.absolutePath)
                mediaRecorder?.start()
                
                // Record for specified duration
                delay(durationSeconds * 1000L)
                
                stopRecording()
                audioFile.absolutePath
            }
        } catch (e: Exception) {
            stopRecording()
            audioFile.delete()
            null
        }
    }
    
    suspend fun recordVideo(durationSeconds: Int = 30): String? {
        if (!hasVideoPermissions()) {
            return null
        }
        
        val videoFile = createVideoFile()
        
        return try {
            withContext(Dispatchers.IO) {
                val cameraId = getBackCameraId() ?: return@withContext null
                
                setupVideoRecorder(videoFile.absolutePath)
                openCamera(cameraId)
                
                // Wait for camera to open
                delay(1000)
                
                if (cameraDevice != null) {
                    startVideoRecording()
                    
                    // Record for specified duration
                    delay(durationSeconds * 1000L)
                    
                    stopRecording()
                    closeCamera()
                    videoFile.absolutePath
                } else {
                    videoFile.delete()
                    null
                }
            }
        } catch (e: Exception) {
            stopRecording()
            closeCamera()
            videoFile.delete()
            null
        }
    }
    
    suspend fun recordAudioAndVideo(durationSeconds: Int = 30): Pair<String?, String?> {
        return if (hasVideoPermissions() && hasAudioPermission()) {
            // Record both simultaneously
            val audioJob = async { recordAudio(durationSeconds) }
            val videoJob = async { recordVideo(durationSeconds) }
            
            Pair(audioJob.await(), videoJob.await())
        } else if (hasAudioPermission()) {
            // Only audio
            Pair(recordAudio(durationSeconds), null)
        } else {
            Pair(null, null)
        }
    }
    
    private fun setupAudioRecorder(outputPath: String) {
        mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(context)
        } else {
            @Suppress("DEPRECATION")
            MediaRecorder()
        }.apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
            setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
            setOutputFile(outputPath)
            prepare()
        }
    }
    
    private fun setupVideoRecorder(outputPath: String) {
        mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            MediaRecorder(context)
        } else {
            @Suppress("DEPRECATION")
            MediaRecorder()
        }.apply {
            setAudioSource(MediaRecorder.AudioSource.MIC)
            setVideoSource(MediaRecorder.VideoSource.SURFACE)
            setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
            setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
            setVideoEncoder(MediaRecorder.VideoEncoder.H264)
            setVideoSize(1280, 720) // 720p
            setVideoFrameRate(30)
            setVideoEncodingBitRate(10000000)
            setOutputFile(outputPath)
            prepare()
        }
    }
    
    private suspend fun openCamera(cameraId: String) = suspendCancellableCoroutine<Unit> { continuation ->
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            continuation.cancel()
            return@suspendCancellableCoroutine
        }
        
        cameraManager?.openCamera(cameraId, object : CameraDevice.StateCallback() {
            override fun onOpened(camera: CameraDevice) {
                cameraDevice = camera
                continuation.resume(Unit)
            }
            
            override fun onDisconnected(camera: CameraDevice) {
                camera.close()
                cameraDevice = null
                continuation.cancel()
            }
            
            override fun onError(camera: CameraDevice, error: Int) {
                camera.close()
                cameraDevice = null
                continuation.cancel()
            }
        }, null)
    }
    
    private fun startVideoRecording() {
        val surface = mediaRecorder?.surface
        if (surface != null && cameraDevice != null) {
            val captureRequestBuilder = cameraDevice!!.createCaptureRequest(CameraDevice.TEMPLATE_RECORD)
            captureRequestBuilder.addTarget(surface)
            
            cameraDevice!!.createCaptureSession(
                listOf(surface),
                object : CameraCaptureSession.StateCallback() {
                    override fun onConfigured(session: CameraCaptureSession) {
                        try {
                            session.setRepeatingRequest(
                                captureRequestBuilder.build(),
                                null,
                                null
                            )
                            mediaRecorder?.start()
                        } catch (e: Exception) {
                            // Handle error
                        }
                    }
                    
                    override fun onConfigureFailed(session: CameraCaptureSession) {
                        // Handle error
                    }
                },
                null
            )
        }
    }
    
    private fun getBackCameraId(): String? {
        return try {
            cameraManager?.cameraIdList?.find { cameraId ->
                val characteristics = cameraManager!!.getCameraCharacteristics(cameraId)
                characteristics.get(CameraCharacteristics.LENS_FACING) == CameraCharacteristics.LENS_FACING_BACK
            }
        } catch (e: Exception) {
            null
        }
    }
    
    private fun stopRecording() {
        try {
            mediaRecorder?.stop()
        } catch (e: Exception) {
            // Recording might not have started
        } finally {
            mediaRecorder?.release()
            mediaRecorder = null
        }
    }
    
    private fun closeCamera() {
        cameraDevice?.close()
        cameraDevice = null
    }
    
    private fun createAudioFile(): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return File(outputDirectory, "emergency_audio_$timestamp.3gp")
    }
    
    private fun createVideoFile(): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return File(outputDirectory, "emergency_video_$timestamp.mp4")
    }
    
    private fun hasAudioPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun hasVideoPermissions(): Boolean {
        return hasAudioPermission() && ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    fun cleanup() {
        recordingJob?.cancel()
        stopRecording()
        closeCamera()
    }
}
