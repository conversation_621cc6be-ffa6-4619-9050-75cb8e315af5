package com.ramstechapp.latram.ui.settings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.ramstechapp.latram.LatramApplication

class SettingsViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = (application as LatramApplication).emergencyRepository

    private val _text = MutableLiveData<String>().apply {
        value = "Settings will be displayed here"
    }
    val text: LiveData<String> = _text
}
