<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- Emergency Button -->
    <ImageButton
        android:id="@+id/widget_emergency_button"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/widget_emergency_button"
        android:contentDescription="@string/cd_emergency_button"
        android:scaleType="centerInside"
        android:src="@drawable/ic_emergency" />

    <!-- Status Text -->
    <TextView
        android:id="@+id/widget_status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="EMERGENCY"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:textStyle="bold" />

</LinearLayout>
