package com.ramstechapp.latram.repository

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import com.ramstechapp.latram.data.database.LatramDatabase
import com.ramstechapp.latram.data.model.*
import com.ramstechapp.latram.data.remote.api.AuthApiService
import com.ramstechapp.latram.data.remote.SupabaseConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.util.*

class AuthRepository(
    private val database: LatramDatabase,
    private val authApi: AuthApiService,
    private val context: Context
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
    
    // User data access
    fun getCurrentUser(): Flow<User?> = database.userDao().getCurrentUser()
    fun getCurrentUserLiveData(): LiveData<User?> = database.userDao().getCurrentUserLiveData()
    suspend fun getCurrentUserSync(): User? = database.userDao().getCurrentUserSync()
    
    // Authentication state
    fun isLoggedIn(): Boolean = getAccessToken() != null && getCurrentUserId() != null
    
    fun getAccessToken(): String? = prefs.getString("access_token", null)
    fun getRefreshToken(): String? = prefs.getString("refresh_token", null)
    fun getCurrentUserId(): String? = prefs.getString("current_user_id", null)
    
    private fun saveTokens(accessToken: String?, refreshToken: String?, userId: String?) {
        prefs.edit().apply {
            putString("access_token", accessToken)
            putString("refresh_token", refreshToken)
            putString("current_user_id", userId)
            apply()
        }
    }
    
    private fun clearTokens() {
        prefs.edit().clear().apply()
    }
    
    // Registration
    suspend fun register(request: RegisterRequest): AuthResponse = withContext(Dispatchers.IO) {
        try {
            val response = authApi.register(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}",
                request = request
            )
            
            if (response.isSuccessful) {
                val authResponse = response.body()!!
                
                if (authResponse.success && authResponse.user != null) {
                    // Save user to local database
                    database.userDao().deactivateAllUsers()
                    database.userDao().insertUser(authResponse.user)
                    database.userDao().activateUser(authResponse.user.id)
                    
                    // Save tokens
                    saveTokens(
                        authResponse.accessToken,
                        authResponse.refreshToken,
                        authResponse.user.id
                    )
                }
                
                authResponse
            } else {
                AuthResponse(
                    success = false,
                    message = "Registration failed: ${response.message()}"
                )
            }
        } catch (e: Exception) {
            AuthResponse(
                success = false,
                message = "Registration error: ${e.message}"
            )
        }
    }
    
    // Login
    suspend fun login(request: LoginRequest): AuthResponse = withContext(Dispatchers.IO) {
        try {
            val response = authApi.login(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}",
                request = request
            )
            
            if (response.isSuccessful) {
                val authResponse = response.body()!!
                
                if (authResponse.success && authResponse.user != null) {
                    // Save user to local database
                    database.userDao().deactivateAllUsers()
                    database.userDao().insertUser(authResponse.user)
                    database.userDao().activateUser(authResponse.user.id)
                    database.userDao().updateLastLogin(authResponse.user.id, System.currentTimeMillis())
                    
                    // Save tokens
                    saveTokens(
                        authResponse.accessToken,
                        authResponse.refreshToken,
                        authResponse.user.id
                    )
                }
                
                authResponse
            } else {
                AuthResponse(
                    success = false,
                    message = "Login failed: ${response.message()}"
                )
            }
        } catch (e: Exception) {
            AuthResponse(
                success = false,
                message = "Login error: ${e.message}"
            )
        }
    }
    
    // Logout
    suspend fun logout(): Boolean = withContext(Dispatchers.IO) {
        try {
            val response = authApi.logout(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${getAccessToken()}"
            )
            
            // Clear local data regardless of API response
            database.userDao().deactivateAllUsers()
            clearTokens()
            
            response.isSuccessful
        } catch (e: Exception) {
            // Clear local data even if API call fails
            database.userDao().deactivateAllUsers()
            clearTokens()
            false
        }
    }
    
    // Forgot Password
    suspend fun forgotPassword(email: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val response = authApi.forgotPassword(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}",
                request = ForgotPasswordRequest(email)
            )
            
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }
    
    // Reset Password
    suspend fun resetPassword(request: ResetPasswordRequest): AuthResponse = withContext(Dispatchers.IO) {
        try {
            val response = authApi.resetPassword(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}",
                request = request
            )
            
            if (response.isSuccessful) {
                response.body()!!
            } else {
                AuthResponse(
                    success = false,
                    message = "Password reset failed: ${response.message()}"
                )
            }
        } catch (e: Exception) {
            AuthResponse(
                success = false,
                message = "Password reset error: ${e.message}"
            )
        }
    }
    
    // Email Verification
    suspend fun verifyEmail(email: String, code: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val response = authApi.verifyEmail(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${getAccessToken()}",
                request = VerifyEmailRequest(email, code)
            )
            
            if (response.isSuccessful) {
                // Update local user
                getCurrentUserSync()?.let { user ->
                    database.userDao().updateEmailVerification(user.id, true)
                }
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    // Resend Verification
    suspend fun resendVerification(email: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val response = authApi.resendVerification(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${getAccessToken()}",
                request = ResendVerificationRequest(email)
            )
            
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }
    
    // Update Profile
    suspend fun updateProfile(user: User): Boolean = withContext(Dispatchers.IO) {
        try {
            val response = authApi.updateProfile(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${getAccessToken()}",
                user = user
            )
            
            if (response.isSuccessful) {
                database.userDao().updateUser(user)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    // Local user operations
    suspend fun updateUser(user: User) = database.userDao().updateUser(user)
    suspend fun getUserByEmail(email: String) = database.userDao().getUserByEmail(email)
    suspend fun getUserById(userId: String) = database.userDao().getUserById(userId)
}
