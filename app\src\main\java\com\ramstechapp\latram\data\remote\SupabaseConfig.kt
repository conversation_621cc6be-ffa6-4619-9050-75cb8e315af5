package com.ramstechapp.latram.data.remote

object SupabaseConfig {
    // TODO: Replace with your actual Supabase project details
    const val SUPABASE_URL = "https://your-project-id.supabase.co"
    const val SUPABASE_ANON_KEY = "your-anon-key-here"
    const val SUPABASE_SERVICE_KEY = "your-service-key-here" // For admin operations
    
    // API Endpoints
    const val ALERTS_ENDPOINT = "/rest/v1/alerts"
    const val STORAGE_ENDPOINT = "/storage/v1/object"
    
    // Storage Buckets
    const val MEDIA_BUCKET = "emergency-media"
    
    // Headers
    const val HEADER_API_KEY = "apikey"
    const val HEADER_AUTHORIZATION = "Authorization"
    const val HEADER_CONTENT_TYPE = "Content-Type"
    const val HEADER_PREFER = "Prefer"
    
    // Content Types
    const val CONTENT_TYPE_JSON = "application/json"
    const val CONTENT_TYPE_OCTET_STREAM = "application/octet-stream"
    
    // Prefer Values
    const val PREFER_RETURN_REPRESENTATION = "return=representation"
    const val PREFER_RETURN_MINIMAL = "return=minimal"
}
