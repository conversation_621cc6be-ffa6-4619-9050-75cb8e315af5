package com.ramstechapp.latram.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.ramstechapp.latram.data.model.EmergencyAlert
import com.ramstechapp.latram.data.model.EmergencyType
import kotlinx.coroutines.flow.Flow

@Dao
interface EmergencyAlertDao {

    @Query("SELECT * FROM emergency_alerts ORDER BY dateTime DESC")
    fun getAllAlerts(): Flow<List<EmergencyAlert>>

    @Query("SELECT * FROM emergency_alerts ORDER BY dateTime DESC")
    fun getAllAlertsLiveData(): LiveData<List<EmergencyAlert>>

    @Query("SELECT * FROM emergency_alerts WHERE id = :alertId")
    suspend fun getAlertById(alertId: String): EmergencyAlert?

    @Query("SELECT * FROM emergency_alerts WHERE syncedToServer = 0")
    suspend fun getUnsyncedAlerts(): List<EmergencyAlert>

    @Query("SELECT * FROM emergency_alerts WHERE emergencyType = :type ORDER BY dateTime DESC")
    fun getAlertsByType(type: EmergencyType): Flow<List<EmergencyAlert>>

    @Query("SELECT * FROM emergency_alerts WHERE dateTime BETWEEN :startDate AND :endDate ORDER BY dateTime DESC")
    fun getAlertsByDateRange(startDate: Long, endDate: Long): Flow<List<EmergencyAlert>>

    @Query("SELECT COUNT(*) FROM emergency_alerts")
    suspend fun getAlertsCount(): Int

    @Query("SELECT COUNT(*) FROM emergency_alerts WHERE syncedToServer = 0")
    suspend fun getUnsyncedAlertsCount(): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlert(alert: EmergencyAlert)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlerts(alerts: List<EmergencyAlert>)

    @Update
    suspend fun updateAlert(alert: EmergencyAlert)

    @Query("UPDATE emergency_alerts SET syncedToServer = 1 WHERE id = :alertId")
    suspend fun markAsSynced(alertId: String)

    @Query("UPDATE emergency_alerts SET mediaUploaded = 1, mediaLink = :mediaLink WHERE id = :alertId")
    suspend fun updateMediaLink(alertId: String, mediaLink: String)

    @Delete
    suspend fun deleteAlert(alert: EmergencyAlert)

    @Query("DELETE FROM emergency_alerts WHERE id = :alertId")
    suspend fun deleteAlertById(alertId: String)

    @Query("DELETE FROM emergency_alerts")
    suspend fun deleteAllAlerts()

    @Query("DELETE FROM emergency_alerts WHERE dateTime < :cutoffDate")
    suspend fun deleteOldAlerts(cutoffDate: Long)

    @Query("SELECT COUNT(*) FROM emergency_alerts")
    suspend fun getAlertsCount(): Int
}
