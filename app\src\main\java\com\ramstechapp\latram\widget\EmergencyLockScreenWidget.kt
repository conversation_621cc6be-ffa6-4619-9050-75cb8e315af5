package com.ramstechapp.latram.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.ramstechapp.latram.R
import com.ramstechapp.latram.ui.emergency.EmergencyLockScreenActivity

/**
 * Lock Screen Emergency Widget
 * Provides quick emergency access directly from lock screen
 */
class EmergencyLockScreenWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        // Update each widget instance
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
    }

    companion object {
        fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // Create intent for emergency access
            val emergencyIntent = Intent(context, EmergencyLockScreenActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
            
            val pendingIntent = PendingIntent.getActivity(
                context, 0, emergencyIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Construct the RemoteViews object
            val views = RemoteViews(context.packageName, R.layout.emergency_lock_screen_widget)
            
            // Set emergency button click action
            views.setOnClickPendingIntent(R.id.widget_emergency_button, pendingIntent)
            
            // Update widget text based on emergency status
            views.setTextViewText(R.id.widget_status_text, "EMERGENCY")
            
            // Set widget background color
            views.setInt(R.id.widget_background, "setBackgroundResource", R.drawable.widget_background)

            // Instruct the widget manager to update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
