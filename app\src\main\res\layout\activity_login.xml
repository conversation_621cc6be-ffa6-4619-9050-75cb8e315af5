<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.auth.LoginActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/on_surface" />

        <!-- Logo -->
        <ImageView
            android:id="@+id/loginLogo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="32dp"
            android:src="@drawable/ic_emergency"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/backButton"
            app:tint="@color/primary" />

        <!-- Title -->
        <TextView
            android:id="@+id/loginTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Welcome Back"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
            android:textColor="@color/on_surface"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loginLogo" />

        <!-- Subtitle -->
        <TextView
            android:id="@+id/loginSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="Sign in to access your emergency features"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="@color/on_surface_variant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loginTitle" />

        <!-- Email Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/emailInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:hint="Email Address"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loginSubtitle"
            app:startIconDrawable="@drawable/ic_email">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/emailInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/passwordInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Password"
            app:endIconMode="password_toggle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/emailInputLayout"
            app:startIconDrawable="@drawable/ic_lock">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/passwordInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Forgot Password -->
        <TextView
            android:id="@+id/forgotPasswordText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Forgot Password?"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="@color/primary"
            android:background="?attr/selectableItemBackground"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/passwordInputLayout" />

        <!-- Login Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/loginButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginTop="32dp"
            android:text="Login"
            android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
            app:cornerRadius="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/forgotPasswordText" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/loginButton"
            app:layout_constraintEnd_toEndOf="@+id/loginButton"
            app:layout_constraintStart_toStartOf="@+id/loginButton"
            app:layout_constraintTop_toTopOf="@+id/loginButton" />

        <!-- Register Link -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loginButton">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Don't have an account? "
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/on_surface_variant" />

            <TextView
                android:id="@+id/registerText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sign Up"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/primary"
                android:textStyle="bold"
                android:background="?attr/selectableItemBackground"
                android:padding="4dp" />

        </LinearLayout>

        <!-- Emergency Access -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/emergencyAccessButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginTop="32dp"
            android:text="🚨 Emergency Access"
            android:textColor="@color/emergency_red"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/registerText" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
