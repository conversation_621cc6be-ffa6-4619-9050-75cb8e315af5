<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <ImageView
                android:id="@+id/alertIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:src="@drawable/ic_emergency"
                app:tint="@color/emergency_red" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/alertType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="General Emergency"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="@color/on_surface" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="2dp">

                    <TextView
                        android:id="@+id/alertDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Dec 25, 2023"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/on_surface_variant" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="•"
                        android:textColor="@color/on_surface_variant" />

                    <TextView
                        android:id="@+id/alertTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="14:30"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/on_surface_variant" />

                </LinearLayout>

            </LinearLayout>

            <!-- Status Icons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/smsStatusIcon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="4dp"
                    android:src="@drawable/ic_sms"
                    android:contentDescription="SMS Status" />

                <ImageView
                    android:id="@+id/callStatusIcon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="4dp"
                    android:src="@drawable/ic_call"
                    android:contentDescription="Call Status" />

                <ImageView
                    android:id="@+id/mediaStatusIcon"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_media"
                    android:contentDescription="Media Status"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!-- Message -->
        <TextView
            android:id="@+id/alertMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Emergency! I need help."
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textColor="@color/on_surface" />

        <!-- Contact Called -->
        <TextView
            android:id="@+id/contactCalled"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Called: +1234567890"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="@color/on_surface_variant" />

        <!-- Location -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/locationText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Location: 0.0, 0.0"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/on_surface_variant" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/viewLocationButton"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="View Map"
                app:icon="@drawable/ic_location" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
