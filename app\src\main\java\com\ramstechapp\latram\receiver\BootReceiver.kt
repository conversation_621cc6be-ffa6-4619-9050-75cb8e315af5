package com.ramstechapp.latram.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import com.ramstechapp.latram.service.FloatingButtonService

class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val PREFS_NAME = "latram_emergency_prefs"
        private const val KEY_FLOATING_BUTTON_ENABLED = "floating_button_enabled"
        private const val KEY_QUICK_ACCESS_ENABLED = "quick_access_enabled"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            "android.intent.action.PACKAGE_REPLACED" -> {
                // Check if floating button was enabled before reboot
                val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val isFloatingButtonEnabled = prefs.getBoolean(KEY_FLOATING_BUTTON_ENABLED, false)
                val isQuickAccessEnabled = prefs.getBoolean(KEY_QUICK_ACCESS_ENABLED, true)
                
                if (isFloatingButtonEnabled || isQuickAccessEnabled) {
                    startFloatingButtonService(context)
                }
            }
        }
    }
    
    private fun startFloatingButtonService(context: Context) {
        try {
            val serviceIntent = Intent(context, FloatingButtonService::class.java).apply {
                action = FloatingButtonService.ACTION_START_FLOATING_BUTTON
            }
            context.startForegroundService(serviceIntent)
        } catch (e: Exception) {
            // Handle any errors starting the service
        }
    }
    
    // Helper methods for managing preferences
    companion object {
        fun setFloatingButtonEnabled(context: Context, enabled: Boolean) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_FLOATING_BUTTON_ENABLED, enabled).apply()
        }
        
        fun isFloatingButtonEnabled(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getBoolean(KEY_FLOATING_BUTTON_ENABLED, false)
        }
        
        fun setQuickAccessEnabled(context: Context, enabled: Boolean) {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putBoolean(KEY_QUICK_ACCESS_ENABLED, enabled).apply()
        }
        
        fun isQuickAccessEnabled(context: Context): Boolean {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            return prefs.getBoolean(KEY_QUICK_ACCESS_ENABLED, true)
        }
    }
}
