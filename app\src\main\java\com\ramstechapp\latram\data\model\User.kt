package com.ramstechapp.latram.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    
    val email: String,
    val fullName: String,
    val phoneNumber: String? = null,
    val profileImageUrl: String? = null,
    
    // Authentication fields
    val isEmailVerified: Boolean = false,
    val isPhoneVerified: Boolean = false,
    val lastLoginAt: Date? = null,
    val passwordResetToken: String? = null,
    val passwordResetExpiry: Date? = null,
    
    // Emergency settings
    val emergencyContactsSetup: Boolean = false,
    val quickAccessEnabled: Boolean = false,
    val stealthModeEnabled: Boolean = false,
    
    // Account status
    val isActive: Boolean = true,
    val accountType: AccountType = AccountType.FREE,
    val subscriptionExpiry: Date? = null,
    
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

enum class AccountType {
    FREE,
    PREMIUM,
    FAMILY,
    ENTERPRISE
}

// Authentication request/response models
data class LoginRequest(
    val email: String,
    val password: String,
    val deviceId: String? = null,
    val fcmToken: String? = null
)

data class RegisterRequest(
    val email: String,
    val password: String,
    val fullName: String,
    val phoneNumber: String? = null,
    val deviceId: String? = null,
    val fcmToken: String? = null
)

data class ForgotPasswordRequest(
    val email: String
)

data class ResetPasswordRequest(
    val token: String,
    val newPassword: String,
    val confirmPassword: String
)

data class AuthResponse(
    val success: Boolean,
    val message: String,
    val user: User? = null,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val expiresIn: Long? = null
)

data class VerifyEmailRequest(
    val email: String,
    val verificationCode: String
)

data class ResendVerificationRequest(
    val email: String
)
