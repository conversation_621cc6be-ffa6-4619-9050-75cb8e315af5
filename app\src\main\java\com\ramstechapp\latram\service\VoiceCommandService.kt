package com.ramstechapp.latram.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.IBinder
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import kotlinx.coroutines.*
import java.util.*

/**
 * Voice Command Emergency Service
 * Listens for emergency voice commands like:
 * - "LATRAM Emergency"
 * - "Help me LATRAM"
 * - "Emergency LATRAM"
 * - "LATRAM msaada" (Swahili)
 */
class VoiceCommandService : Service(), RecognitionListener {
    
    companion object {
        const val ACTION_START_VOICE_LISTENING = "com.ramstechapp.latram.START_VOICE_LISTENING"
        const val ACTION_STOP_VOICE_LISTENING = "com.ramstechapp.latram.STOP_VOICE_LISTENING"
        
        // Emergency trigger phrases
        private val EMERGENCY_PHRASES = listOf(
            "latram emergency",
            "emergency latram",
            "help me latram",
            "latram help",
            "latram msaada",
            "msaada latram",
            "latram hapa",
            "emergency help"
        )
    }
    
    private var speechRecognizer: SpeechRecognizer? = null
    private var isListening = false
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    override fun onCreate() {
        super.onCreate()
        initializeSpeechRecognizer()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_VOICE_LISTENING -> {
                startVoiceListening()
            }
            ACTION_STOP_VOICE_LISTENING -> {
                stopVoiceListening()
                stopSelf()
            }
        }
        return START_STICKY
    }
    
    private fun initializeSpeechRecognizer() {
        if (SpeechRecognizer.isRecognitionAvailable(this)) {
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this)
            speechRecognizer?.setRecognitionListener(this)
        }
    }
    
    private fun startVoiceListening() {
        if (isListening || speechRecognizer == null) return
        
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
            putExtra(RecognizerIntent.EXTRA_CALLING_PACKAGE, packageName)
        }
        
        try {
            speechRecognizer?.startListening(intent)
            isListening = true
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    private fun stopVoiceListening() {
        if (!isListening) return
        
        try {
            speechRecognizer?.stopListening()
            isListening = false
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    override fun onResults(results: Bundle?) {
        val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
        matches?.forEach { result ->
            if (isEmergencyPhrase(result)) {
                triggerVoiceEmergency(result)
                return
            }
        }
        
        // Restart listening for continuous monitoring
        serviceScope.launch {
            delay(1000) // Brief pause
            if (isListening) {
                startVoiceListening()
            }
        }
    }
    
    override fun onPartialResults(partialResults: Bundle?) {
        val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
        matches?.forEach { result ->
            if (isEmergencyPhrase(result)) {
                triggerVoiceEmergency(result)
                return
            }
        }
    }
    
    private fun isEmergencyPhrase(spokenText: String): Boolean {
        val normalizedText = spokenText.lowercase().trim()
        return EMERGENCY_PHRASES.any { phrase ->
            normalizedText.contains(phrase) || 
            normalizedText.replace(" ", "").contains(phrase.replace(" ", ""))
        }
    }
    
    private fun triggerVoiceEmergency(spokenText: String) {
        serviceScope.launch {
            try {
                // Stop listening to prevent multiple triggers
                stopVoiceListening()
                
                // Trigger emergency service
                val emergencyIntent = Intent(this@VoiceCommandService, EmergencyService::class.java).apply {
                    action = EmergencyService.ACTION_TRIGGER_EMERGENCY
                    putExtra(EmergencyService.EXTRA_EMERGENCY_MESSAGE, 
                        "Emergency triggered by voice command: '$spokenText'")
                    putExtra(EmergencyService.EXTRA_EMERGENCY_TYPE, "GENERAL")
                }
                startForegroundService(emergencyIntent)
                
            } catch (e: Exception) {
                // Silent failure
            }
        }
    }
    
    override fun onError(error: Int) {
        when (error) {
            SpeechRecognizer.ERROR_AUDIO -> {
                // Audio recording error
            }
            SpeechRecognizer.ERROR_CLIENT -> {
                // Client side error
            }
            SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> {
                // Insufficient permissions
            }
            SpeechRecognizer.ERROR_NETWORK -> {
                // Network error
            }
            SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> {
                // Network timeout
            }
            SpeechRecognizer.ERROR_NO_MATCH -> {
                // No recognition result matched
            }
            SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> {
                // RecognitionService busy
            }
            SpeechRecognizer.ERROR_SERVER -> {
                // Server error
            }
            SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> {
                // No speech input
            }
        }
        
        // Restart listening after error (except for permission errors)
        if (error != SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS) {
            serviceScope.launch {
                delay(2000) // Wait 2 seconds before restarting
                if (isListening) {
                    startVoiceListening()
                }
            }
        }
    }
    
    override fun onReadyForSpeech(params: Bundle?) {}
    override fun onBeginningOfSpeech() {}
    override fun onRmsChanged(rmsdB: Float) {}
    override fun onBufferReceived(buffer: ByteArray?) {}
    override fun onEndOfSpeech() {}
    
    override fun onDestroy() {
        super.onDestroy()
        stopVoiceListening()
        speechRecognizer?.destroy()
        serviceScope.cancel()
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
}
