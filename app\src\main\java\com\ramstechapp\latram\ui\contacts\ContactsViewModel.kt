package com.ramstechapp.latram.ui.contacts

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ramstechapp.latram.LatramApplication
import com.ramstechapp.latram.data.model.EmergencyContact
import com.ramstechapp.latram.service.ContactBackupService
import com.ramstechapp.latram.utils.Event
import kotlinx.coroutines.launch

class ContactsViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = (application as LatramApplication).emergencyRepository
    private val contactBackupService = ContactBackupService(application)

    val contacts = repository.getAllContacts()

    private val _showToast = MutableLiveData<Event<String>>()
    val showToast: LiveData<Event<String>> = _showToast

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    fun addContact(contact: EmergencyContact) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // If this is set as primary, clear other primary contacts
                if (contact.isPrimary) {
                    repository.clearPrimaryContacts()
                }
                
                repository.insertContact(contact)
                _showToast.value = Event("Contact added successfully")
                
            } catch (e: Exception) {
                _showToast.value = Event("Failed to add contact: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun updateContact(contact: EmergencyContact) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // If this is set as primary, clear other primary contacts
                if (contact.isPrimary) {
                    repository.clearPrimaryContacts()
                }
                
                repository.updateContact(contact)
                _showToast.value = Event("Contact updated successfully")
                
            } catch (e: Exception) {
                _showToast.value = Event("Failed to update contact: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun deleteContact(contact: EmergencyContact) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.deleteContact(contact)
                _showToast.value = Event("Contact deleted successfully")
                
            } catch (e: Exception) {
                _showToast.value = Event("Failed to delete contact: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun setPrimaryContact(contactId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Clear all primary contacts first
                repository.clearPrimaryContacts()
                
                // Set the selected contact as primary
                repository.setPrimaryContact(contactId)
                _showToast.value = Event("Primary contact updated")
                
            } catch (e: Exception) {
                _showToast.value = Event("Failed to set primary contact: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun backupContacts() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = contactBackupService.createFullBackup()
                
                if (result.success) {
                    _showToast.value = Event("Backup created: ${result.message}")
                } else {
                    _showToast.value = Event("Backup failed: ${result.message}")
                }
                
            } catch (e: Exception) {
                _showToast.value = Event("Backup failed: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun restoreFromLocal() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = contactBackupService.restoreFromBackup(
                    com.ramstechapp.latram.service.BackupSource.LOCAL
                )
                
                if (result.success) {
                    _showToast.value = Event("Restored ${result.contactsRestored} contacts from local backup")
                } else {
                    _showToast.value = Event("Restore failed: ${result.message}")
                }
                
            } catch (e: Exception) {
                _showToast.value = Event("Restore failed: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun restoreFromDrive() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = contactBackupService.restoreFromBackup(
                    com.ramstechapp.latram.service.BackupSource.GOOGLE_DRIVE
                )
                
                if (result.success) {
                    _showToast.value = Event("Restored ${result.contactsRestored} contacts from Google Drive")
                } else {
                    _showToast.value = Event("Restore failed: ${result.message}")
                }
                
            } catch (e: Exception) {
                _showToast.value = Event("Restore failed: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun restoreFromCloud() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val result = contactBackupService.restoreFromBackup(
                    com.ramstechapp.latram.service.BackupSource.CLOUD
                )
                
                if (result.success) {
                    _showToast.value = Event("Restored ${result.contactsRestored} contacts from cloud")
                } else {
                    _showToast.value = Event("Restore failed: ${result.message}")
                }
                
            } catch (e: Exception) {
                _showToast.value = Event("Restore failed: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }
}
