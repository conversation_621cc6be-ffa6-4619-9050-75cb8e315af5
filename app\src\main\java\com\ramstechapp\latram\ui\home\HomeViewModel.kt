package com.ramstechapp.latram.ui.home

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ramstechapp.latram.LatramApplication
import com.ramstechapp.latram.utils.Event
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class HomeViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = (application as LatramApplication).emergencyRepository
    private val locationService = (application as LatramApplication).locationService

    private val _statusText = MutableLiveData<String>()
    val statusText: LiveData<String> = _statusText

    private val _lastAlertText = MutableLiveData<String>()
    val lastAlertText: LiveData<String> = _lastAlertText
    
    private val _showToast = MutableLiveData<Event<String>>()
    val showToast: LiveData<Event<String>> = _showToast

    init {
        loadStatus()
        loadLastAlert()
    }

    private fun loadStatus() {
        viewModelScope.launch {
            try {
                val contactsCount = repository.getActiveContactsCount()
                val alertsCount = repository.getAlertsCount()
                
                _statusText.value = when {
                    contactsCount == 0 -> "⚠️ No emergency contacts configured"
                    alertsCount == 0 -> "✅ Ready for emergency (No alerts sent yet)"
                    else -> "✅ Ready for emergency ($alertsCount alerts sent)"
                }
            } catch (e: Exception) {
                _statusText.value = "❌ Error loading status"
            }
        }
    }

    private fun loadLastAlert() {
        viewModelScope.launch {
            try {
                repository.getAllAlerts().collect { alerts ->
                    if (alerts.isEmpty()) {
                        _lastAlertText.value = "No recent alerts"
                    } else {
                        val lastAlert = alerts.first()
                        val dateFormat = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault())
                        val formattedDate = dateFormat.format(lastAlert.dateTime)
                        _lastAlertText.value = "Last alert: $formattedDate"
                    }
                }
            } catch (e: Exception) {
                _lastAlertText.value = "Error loading alert history"
            }
        }
    }

    fun testLocation() {
        viewModelScope.launch {
            try {
                _showToast.value = Event("Getting location...")
                val location = locationService.getCurrentLocation()
                
                if (location != null) {
                    val message = "Location found: ${location.latitude}, ${location.longitude}\n" +
                                "Accuracy: ±${location.accuracy.toInt()}m"
                    _showToast.value = Event(message)
                } else {
                    _showToast.value = Event("Failed to get location. Check permissions and GPS.")
                }
            } catch (e: Exception) {
                _showToast.value = Event("Location error: ${e.message}")
            }
        }
    }

    fun refreshStatus() {
        loadStatus()
        loadLastAlert()
    }
}
