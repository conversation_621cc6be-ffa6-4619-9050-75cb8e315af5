package com.ramstechapp.latram.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.ramstechapp.latram.data.model.UserSettings
import kotlinx.coroutines.flow.Flow

@Dao
interface UserSettingsDao {
    
    @Query("SELECT * FROM user_settings WHERE id = 'default' LIMIT 1")
    fun getSettings(): Flow<UserSettings?>
    
    @Query("SELECT * FROM user_settings WHERE id = 'default' LIMIT 1")
    fun getSettingsLiveData(): LiveData<UserSettings?>
    
    @Query("SELECT * FROM user_settings WHERE id = 'default' LIMIT 1")
    suspend fun getSettingsSync(): UserSettings?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: UserSettings)
    
    @Update
    suspend fun updateSettings(settings: UserSettings)
    
    @Query("UPDATE user_settings SET defaultMessage = :message WHERE id = 'default'")
    suspend fun updateDefaultMessage(message: String)
    
    @Query("UPDATE user_settings SET autoCallEnabled = :enabled WHERE id = 'default'")
    suspend fun updateAutoCallEnabled(enabled: Boolean)
    
    @Query("UPDATE user_settings SET autoRecordEnabled = :enabled WHERE id = 'default'")
    suspend fun updateAutoRecordEnabled(enabled: Boolean)
    
    @Query("UPDATE user_settings SET googleDriveEnabled = :enabled WHERE id = 'default'")
    suspend fun updateGoogleDriveEnabled(enabled: Boolean)
    
    @Query("UPDATE user_settings SET ttsEnabled = :enabled WHERE id = 'default'")
    suspend fun updateTtsEnabled(enabled: Boolean)
    
    @Query("UPDATE user_settings SET recordingDuration = :duration WHERE id = 'default'")
    suspend fun updateRecordingDuration(duration: Int)
    
    @Query("UPDATE user_settings SET lastBackupDate = :date WHERE id = 'default'")
    suspend fun updateLastBackupDate(date: Long)
    
    @Delete
    suspend fun deleteSettings(settings: UserSettings)
    
    @Query("DELETE FROM user_settings")
    suspend fun deleteAllSettings()
}
