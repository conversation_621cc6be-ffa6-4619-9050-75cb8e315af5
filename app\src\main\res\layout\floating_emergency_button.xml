<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <!-- Floating Emergency Button -->
    <ImageView
        android:id="@+id/floatingEmergencyButton"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:background="@drawable/floating_button_background"
        android:contentDescription="@string/cd_emergency_button"
        android:padding="12dp"
        android:src="@drawable/ic_emergency"
        android:tint="@android:color/white"
        android:elevation="8dp"
        android:clickable="true"
        android:focusable="true" />

    <!-- Pulse Animation Ring -->
    <View
        android:id="@+id/pulseRing"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_gravity="center"
        android:background="@drawable/pulse_ring"
        android:alpha="0.6" />

</FrameLayout>
