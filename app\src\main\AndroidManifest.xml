<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Emergency Communication Permissions -->
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <!-- Location Permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- Media Recording Permissions -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- Network Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Google Drive and Account Permissions -->
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />

    <!-- Foreground Service for Emergency Operations -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- Vibration for Emergency Alerts -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Overlay and Lock Screen Access -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <!-- Boot and Device Admin for Persistent Access -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- Voice Recognition for Voice Commands -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- Gesture Detection -->
    <uses-permission android:name="android.permission.BODY_SENSORS" />

    <!-- Social Media Sharing -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <application
        android:name=".LatramApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LATRAM"
        tools:targetApi="31">

        <!-- Welcome Activity (Launcher) -->
        <activity
            android:name=".ui.auth.WelcomeActivity"
            android:exported="true"
            android:theme="@style/Theme.LATRAM">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Authentication Activities -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.LATRAM"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ui.auth.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.LATRAM"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ui.auth.ForgotPasswordActivity"
            android:exported="false"
            android:theme="@style/Theme.LATRAM"
            android:windowSoftInputMode="adjustResize" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:theme="@style/Theme.LATRAM" />

        <!-- Emergency Service -->
        <service
            android:name=".service.EmergencyService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location|microphone|camera" />

        <!-- Floating Emergency Button Service -->
        <service
            android:name=".service.FloatingButtonService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" />

        <!-- Stealth Recording Service -->
        <service
            android:name=".service.StealthRecordingService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone|camera" />

        <!-- Gesture Detection Service -->
        <service
            android:name=".service.GestureDetectionService"
            android:enabled="true"
            android:exported="false" />

        <!-- Voice Command Service -->
        <service
            android:name=".service.VoiceCommandService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="microphone" />

        <!-- Emergency Lock Screen Activity -->
        <activity
            android:name=".ui.emergency.EmergencyLockScreenActivity"
            android:exported="false"
            android:showWhenLocked="true"
            android:turnScreenOn="true"
            android:excludeFromRecents="true"
            android:taskAffinity=""
            android:launchMode="singleInstance"
            android:theme="@style/Theme.LATRAM.Emergency" />

        <!-- Boot Receiver -->
        <receiver
            android:name=".receiver.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- Power Button Emergency Receiver -->
        <receiver
            android:name=".receiver.PowerButtonReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.SCREEN_OFF" />
                <action android:name="android.intent.action.SCREEN_ON" />
            </intent-filter>
        </receiver>

        <!-- Emergency Lock Screen Widget -->
        <receiver
            android:name=".widget.EmergencyLockScreenWidget"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/emergency_lock_screen_widget_info" />
        </receiver>

        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="${MAPS_API_KEY}" />

        <!-- File Provider for sharing media files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>
