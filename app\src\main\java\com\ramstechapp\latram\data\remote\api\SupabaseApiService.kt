package com.ramstechapp.latram.data.remote.api

import com.ramstechapp.latram.data.remote.dto.AlertDto
import com.ramstechapp.latram.data.remote.dto.CreateAlertRequest
import com.ramstechapp.latram.data.remote.dto.UpdateAlertRequest
import okhttp3.MultipartBody
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.*

interface SupabaseApiService {
    
    // Alert operations
    @POST("rest/v1/alerts")
    @Headers("Prefer: return=representation")
    suspend fun createAlert(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body alert: CreateAlertRequest
    ): Response<List<AlertDto>>
    
    @GET("rest/v1/alerts")
    suspend fun getAllAlerts(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Query("select") select: String = "*",
        @Query("order") order: String = "date_time.desc"
    ): Response<List<AlertDto>>
    
    @GET("rest/v1/alerts")
    suspend fun getAlertById(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Query("id") id: String,
        @Query("select") select: String = "*"
    ): Response<List<AlertDto>>
    
    @PATCH("rest/v1/alerts")
    suspend fun updateAlert(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Query("id") id: String,
        @Body updateRequest: UpdateAlertRequest
    ): Response<ResponseBody>
    
    @DELETE("rest/v1/alerts")
    suspend fun deleteAlert(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Query("id") id: String
    ): Response<ResponseBody>
    
    // Storage operations
    @Multipart
    @POST("storage/v1/object/{bucket}")
    suspend fun uploadFile(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Path("bucket") bucket: String,
        @Part file: MultipartBody.Part,
        @Query("upsert") upsert: Boolean = true
    ): Response<ResponseBody>
    
    @GET("storage/v1/object/{bucket}/{path}")
    suspend fun downloadFile(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Path("bucket") bucket: String,
        @Path("path") path: String
    ): Response<ResponseBody>
    
    @DELETE("storage/v1/object/{bucket}/{path}")
    suspend fun deleteFile(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Path("bucket") bucket: String,
        @Path("path") path: String
    ): Response<ResponseBody>
    
    @GET("storage/v1/object/info/{bucket}/{path}")
    suspend fun getFileInfo(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Path("bucket") bucket: String,
        @Path("path") path: String
    ): Response<ResponseBody>
}
