<resources>
    <string name="app_name">LATRAM</string>
    <string name="title_home">Emergency</string>
    <string name="title_history">History</string>
    <string name="title_settings">Settings</string>

    <!-- Emergency Types -->
    <string name="emergency_general">General Emergency</string>
    <string name="emergency_medical">Medical Emergency</string>
    <string name="emergency_fire">Fire Emergency</string>
    <string name="emergency_police">Police Emergency</string>
    <string name="emergency_accident">Accident</string>
    <string name="emergency_natural_disaster">Natural Disaster</string>

    <!-- Emergency Messages -->
    <string name="default_emergency_message">Emergency! I need help. Please check my location.</string>
    <string name="emergency_triggered">Emergency alert triggered</string>
    <string name="emergency_cancelled">Emergency alert cancelled</string>

    <!-- Permissions -->
    <string name="permission_sms_rationale">SMS permission is required to send emergency messages</string>
    <string name="permission_call_rationale">Phone permission is required to make emergency calls</string>
    <string name="permission_location_rationale">Location permission is required to share your location in emergencies</string>
    <string name="permission_camera_rationale">Camera permission is required to record emergency videos</string>
    <string name="permission_microphone_rationale">Microphone permission is required to record emergency audio</string>

    <!-- Settings -->
    <string name="settings_emergency_contacts">Emergency Contacts</string>
    <string name="settings_default_message">Default Emergency Message</string>
    <string name="settings_auto_call">Auto Call After SMS</string>
    <string name="settings_auto_record">Auto Record Media</string>
    <string name="settings_google_drive">Google Drive Upload</string>
    <string name="settings_text_to_speech">Text-to-Speech</string>
    <string name="settings_recording_duration">Recording Duration (seconds)</string>

    <!-- Stealth Settings -->
    <string name="settings_stealth_recording">Stealth Recording</string>
    <string name="settings_stealth_recording_desc">Record covertly without visible indicators</string>
    <string name="settings_real_time_upload">Real-time Upload</string>
    <string name="settings_real_time_upload_desc">Upload recordings immediately to prevent deletion</string>
    <string name="settings_silent_recording">Silent Recording</string>
    <string name="settings_silent_recording_desc">Disable camera sounds and visual indicators</string>
    <string name="settings_auto_delete">Auto Delete After Upload</string>
    <string name="settings_auto_delete_desc">Remove local files after successful upload</string>
    <string name="settings_stealth_quality">Recording Quality</string>

    <!-- Contacts -->
    <string name="contact_name">Contact Name</string>
    <string name="contact_phone">Phone Number</string>
    <string name="contact_relationship">Relationship</string>
    <string name="contact_primary">Primary Contact</string>
    <string name="add_contact">Add Contact</string>
    <string name="edit_contact">Edit Contact</string>
    <string name="delete_contact">Delete Contact</string>

    <!-- History -->
    <string name="history_empty">No emergency alerts yet</string>
    <string name="history_sms_sent">SMS Sent</string>
    <string name="history_call_made">Call Made</string>
    <string name="history_media_recorded">Media Recorded</string>
    <string name="history_synced">Synced to Cloud</string>

    <!-- Errors -->
    <string name="error_no_contacts">Please add an emergency contact first</string>
    <string name="error_location_failed">Failed to get location</string>
    <string name="error_sms_failed">Failed to send SMS</string>
    <string name="error_call_failed">Failed to make call</string>
    <string name="error_recording_failed">Failed to record media</string>
    <string name="error_upload_failed">Failed to upload to Google Drive</string>

    <!-- Notifications -->
    <string name="notification_emergency_active">Emergency alert in progress</string>
    <string name="notification_emergency_completed">Emergency alert completed</string>

    <!-- Buttons -->
    <string name="button_emergency">EMERGENCY</string>
    <string name="button_cancel">Cancel</string>
    <string name="button_save">Save</string>
    <string name="button_test">Test</string>
    <string name="button_retry">Retry</string>
    <string name="button_settings">Settings</string>

    <!-- Content Descriptions -->
    <string name="cd_emergency_button">Emergency alert button</string>
    <string name="cd_location_icon">Location icon</string>
    <string name="cd_contact_icon">Contact icon</string>
    <string name="cd_history_icon">History icon</string>
    <string name="cd_settings_icon">Settings icon</string>

    <!-- Widget -->
    <string name="widget_description">Emergency alert widget for quick access</string>
    <string name="widget_emergency_text">EMERGENCY</string>

    <!-- Voice Commands -->
    <string name="voice_command_enabled">Voice commands enabled</string>
    <string name="voice_command_disabled">Voice commands disabled</string>
    <string name="voice_command_listening">Listening for emergency commands...</string>

    <!-- Gesture Detection -->
    <string name="gesture_detection_enabled">Gesture detection enabled</string>
    <string name="gesture_detection_disabled">Gesture detection disabled</string>
    <string name="shake_to_emergency">Shake phone 5 times for emergency</string>

    <!-- Power Button Emergency -->
    <string name="power_button_emergency_enabled">Power button emergency enabled</string>
    <string name="power_button_emergency_disabled">Power button emergency disabled</string>
    <string name="power_button_instruction">Press power button 5 times quickly for emergency</string>

    <!-- Social Media Sharing -->
    <string name="share_via_whatsapp">Share via WhatsApp</string>
    <string name="share_via_telegram">Share via Telegram</string>
    <string name="share_via_social">Share via Social Media</string>
    <string name="social_sharing_enabled">Social media sharing enabled</string>

    <!-- Backup & Restore -->
    <string name="backup_contacts">Backup Emergency Contacts</string>
    <string name="restore_contacts">Restore Emergency Contacts</string>
    <string name="backup_successful">Backup created successfully</string>
    <string name="restore_successful">Contacts restored successfully</string>
    <string name="backup_failed">Backup failed</string>
    <string name="restore_failed">Restore failed</string>
</resources>
