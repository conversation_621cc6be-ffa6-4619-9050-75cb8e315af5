package com.ramstechapp.latram.ui.history

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ramstechapp.latram.R
import com.ramstechapp.latram.databinding.ItemEmergencyAlertBinding
import com.ramstechapp.latram.data.model.EmergencyAlert
import com.ramstechapp.latram.data.model.EmergencyType
import java.text.SimpleDateFormat
import java.util.*

class EmergencyAlertsAdapter(
    private val onAlertClick: (EmergencyAlert) -> Unit
) : ListAdapter<EmergencyAlert, EmergencyAlertsAdapter.AlertViewHolder>(AlertDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AlertViewHolder {
        val binding = ItemEmergencyAlertBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AlertViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON>ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class AlertViewHolder(
        private val binding: ItemEmergencyAlertBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(alert: EmergencyAlert) {
            binding.apply {
                // Format date and time
                val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
                val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                
                alertDate.text = dateFormat.format(alert.dateTime)
                alertTime.text = timeFormat.format(alert.dateTime)
                
                // Set emergency type and icon
                alertType.text = getEmergencyTypeText(alert.emergencyType)
                alertIcon.setImageResource(getEmergencyTypeIcon(alert.emergencyType))
                
                // Set message
                alertMessage.text = alert.message
                
                // Set contact called
                contactCalled.text = "Called: ${alert.contactCalled}"
                
                // Set status indicators
                smsStatusIcon.setImageResource(
                    if (alert.smsDelivered) R.drawable.ic_check_circle 
                    else R.drawable.ic_error_circle
                )
                smsStatusIcon.setColorFilter(
                    if (alert.smsDelivered) 
                        root.context.getColor(R.color.success_green)
                    else 
                        root.context.getColor(R.color.error)
                )
                
                callStatusIcon.setImageResource(
                    if (alert.callMade) R.drawable.ic_check_circle 
                    else R.drawable.ic_error_circle
                )
                callStatusIcon.setColorFilter(
                    if (alert.callMade) 
                        root.context.getColor(R.color.success_green)
                    else 
                        root.context.getColor(R.color.error)
                )
                
                // Media status
                if (alert.audioFilePath != null || alert.videoFilePath != null) {
                    mediaStatusIcon.visibility = android.view.View.VISIBLE
                    mediaStatusIcon.setImageResource(R.drawable.ic_media)
                    mediaStatusIcon.setColorFilter(root.context.getColor(R.color.success_green))
                } else {
                    mediaStatusIcon.visibility = android.view.View.GONE
                }
                
                // Location
                locationText.text = "Location: ${alert.latitude}, ${alert.longitude}"
                
                // Click listener
                root.setOnClickListener { onAlertClick(alert) }
                
                // Location button
                viewLocationButton.setOnClickListener {
                    val intent = android.content.Intent(android.content.Intent.ACTION_VIEW).apply {
                        data = android.net.Uri.parse("https://maps.google.com/?q=${alert.latitude},${alert.longitude}")
                    }
                    root.context.startActivity(intent)
                }
            }
        }
        
        private fun getEmergencyTypeText(type: EmergencyType): String {
            return when (type) {
                EmergencyType.GENERAL -> "General Emergency"
                EmergencyType.MEDICAL -> "Medical Emergency"
                EmergencyType.FIRE -> "Fire Emergency"
                EmergencyType.POLICE -> "Police Emergency"
                EmergencyType.ACCIDENT -> "Accident"
                EmergencyType.NATURAL_DISASTER -> "Natural Disaster"
            }
        }
        
        private fun getEmergencyTypeIcon(type: EmergencyType): Int {
            return when (type) {
                EmergencyType.GENERAL -> R.drawable.ic_emergency
                EmergencyType.MEDICAL -> R.drawable.ic_medical
                EmergencyType.FIRE -> R.drawable.ic_fire
                EmergencyType.POLICE -> R.drawable.ic_police
                EmergencyType.ACCIDENT -> R.drawable.ic_accident
                EmergencyType.NATURAL_DISASTER -> R.drawable.ic_disaster
            }
        }
    }

    private class AlertDiffCallback : DiffUtil.ItemCallback<EmergencyAlert>() {
        override fun areItemsTheSame(oldItem: EmergencyAlert, newItem: EmergencyAlert): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: EmergencyAlert, newItem: EmergencyAlert): Boolean {
            return oldItem == newItem
        }
    }
}
