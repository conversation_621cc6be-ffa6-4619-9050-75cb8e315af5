package com.ramstechapp.latram.data.remote.api

import com.ramstechapp.latram.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface AuthApiService {

    @POST("auth/register")
    suspend fun register(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: RegisterRequest
    ): Response<AuthResponse>

    @POST("auth/login")
    suspend fun login(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: LoginRequest
    ): Response<AuthResponse>

    @POST("auth/logout")
    suspend fun logout(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: Map<String, String> = emptyMap()
    ): Response<Map<String, Any>>

    @POST("auth/forgot-password")
    suspend fun forgotPassword(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: ForgotPasswordRequest
    ): Response<Map<String, Any>>

    @POST("auth/reset-password")
    suspend fun resetPassword(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: ResetPasswordRequest
    ): Response<AuthResponse>

    @POST("auth/verify-email")
    suspend fun verifyEmail(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: VerifyEmailRequest
    ): Response<Map<String, Any>>

    @POST("auth/resend-verification")
    suspend fun resendVerification(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: ResendVerificationRequest
    ): Response<Map<String, Any>>

    @POST("auth/refresh-token")
    suspend fun refreshToken(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: Map<String, String>
    ): Response<AuthResponse>

    @GET("auth/profile")
    suspend fun getProfile(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String
    ): Response<User>

    @PUT("auth/profile")
    suspend fun updateProfile(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body user: User
    ): Response<User>

    @POST("auth/change-password")
    suspend fun changePassword(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: Map<String, String>
    ): Response<Map<String, Any>>

    @DELETE("auth/account")
    suspend fun deleteAccount(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: Map<String, String>
    ): Response<Map<String, Any>>

    @POST("auth/verify-phone")
    suspend fun verifyPhone(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: Map<String, String>
    ): Response<Map<String, Any>>

    @POST("auth/send-phone-verification")
    suspend fun sendPhoneVerification(
        @Header("apikey") apiKey: String,
        @Header("Authorization") authorization: String,
        @Body request: Map<String, String>
    ): Response<Map<String, Any>>
}
