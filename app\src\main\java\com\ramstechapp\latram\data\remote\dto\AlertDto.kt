package com.ramstechapp.latram.data.remote.dto

import com.google.gson.annotations.SerializedName
import com.ramstechapp.latram.data.model.EmergencyAlert
import com.ramstechapp.latram.data.model.EmergencyType
import java.util.Date

data class AlertDto(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("user_id")
    val userId: String? = null,
    
    @SerializedName("location")
    val location: String,
    
    @SerializedName("latitude")
    val latitude: Double,
    
    @SerializedName("longitude")
    val longitude: Double,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("date_time")
    val dateTime: String, // ISO 8601 format
    
    @SerializedName("media_link")
    val mediaLink: String? = null,
    
    @SerializedName("contact_called")
    val contactCalled: String,
    
    @SerializedName("emergency_type")
    val emergencyType: String = "GENERAL",
    
    @SerializedName("sms_delivered")
    val smsDelivered: Boolean = false,
    
    @SerializedName("call_made")
    val callMade: Boolean = false,
    
    @SerializedName("media_uploaded")
    val mediaUploaded: Boolean = false,
    
    @SerializedName("created_at")
    val createdAt: String? = null,
    
    @SerializedName("updated_at")
    val updatedAt: String? = null
)

data class CreateAlertRequest(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("user_id")
    val userId: String? = null,
    
    @SerializedName("location")
    val location: String,
    
    @SerializedName("latitude")
    val latitude: Double,
    
    @SerializedName("longitude")
    val longitude: Double,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("date_time")
    val dateTime: String,
    
    @SerializedName("media_link")
    val mediaLink: String? = null,
    
    @SerializedName("contact_called")
    val contactCalled: String,
    
    @SerializedName("emergency_type")
    val emergencyType: String = "GENERAL",
    
    @SerializedName("sms_delivered")
    val smsDelivered: Boolean = false,
    
    @SerializedName("call_made")
    val callMade: Boolean = false,
    
    @SerializedName("media_uploaded")
    val mediaUploaded: Boolean = false
)

data class UpdateAlertRequest(
    @SerializedName("media_link")
    val mediaLink: String? = null,
    
    @SerializedName("sms_delivered")
    val smsDelivered: Boolean? = null,
    
    @SerializedName("call_made")
    val callMade: Boolean? = null,
    
    @SerializedName("media_uploaded")
    val mediaUploaded: Boolean? = null
)

// Extension functions for conversion
fun EmergencyAlert.toCreateAlertRequest(): CreateAlertRequest {
    return CreateAlertRequest(
        id = this.id,
        userId = this.userId,
        location = this.location,
        latitude = this.latitude,
        longitude = this.longitude,
        message = this.message,
        dateTime = this.dateTime.toISOString(),
        mediaLink = this.mediaLink,
        contactCalled = this.contactCalled,
        emergencyType = this.emergencyType.name,
        smsDelivered = this.smsDelivered,
        callMade = this.callMade,
        mediaUploaded = this.mediaUploaded
    )
}

fun AlertDto.toEmergencyAlert(): EmergencyAlert {
    return EmergencyAlert(
        id = this.id,
        userId = this.userId,
        location = this.location,
        latitude = this.latitude,
        longitude = this.longitude,
        message = this.message,
        dateTime = Date.fromISOString(this.dateTime),
        mediaLink = this.mediaLink,
        contactCalled = this.contactCalled,
        emergencyType = EmergencyType.valueOf(this.emergencyType),
        smsDelivered = this.smsDelivered,
        callMade = this.callMade,
        mediaUploaded = this.mediaUploaded,
        syncedToServer = true
    )
}

// Helper extension functions for date conversion
private fun Date.toISOString(): String {
    return java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.US).format(this)
}

private fun Date.Companion.fromISOString(isoString: String): Date {
    return java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.US).parse(isoString) ?: Date()
}
