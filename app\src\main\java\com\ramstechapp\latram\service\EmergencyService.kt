package com.ramstechapp.latram.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.location.Location
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.ramstechapp.latram.R
import com.ramstechapp.latram.data.database.LatramDatabase
import com.ramstechapp.latram.data.model.EmergencyAlert
import com.ramstechapp.latram.data.model.EmergencyContact
import com.ramstechapp.latram.data.model.EmergencyType
import kotlinx.coroutines.*
import java.util.*

class EmergencyService : Service() {

    companion object {
        const val ACTION_TRIGGER_EMERGENCY = "com.ramstechapp.latram.TRIGGER_EMERGENCY"
        const val ACTION_CANCEL_EMERGENCY = "com.ramstechapp.latram.CANCEL_EMERGENCY"
        const val EXTRA_EMERGENCY_MESSAGE = "emergency_message"
        const val EXTRA_EMERGENCY_TYPE = "emergency_type"
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "emergency_channel"
    }

    private lateinit var locationService: LocationService
    private lateinit var smsService: SmsService
    private lateinit var callService: CallService
    private lateinit var mediaRecordingService: MediaRecordingService
    private lateinit var googleDriveService: GoogleDriveService
    private lateinit var database: LatramDatabase

    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var emergencyJob: Job? = null

    override fun onCreate() {
        super.onCreate()
        initializeServices()
        createNotificationChannel()
    }

    private fun initializeServices() {
        locationService = LocationService(this)
        smsService = SmsService(this)
        callService = CallService(this)
        mediaRecordingService = MediaRecordingService(this)
        googleDriveService = GoogleDriveService(this)
        database = LatramDatabase.getDatabase(this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_TRIGGER_EMERGENCY -> {
                val message = intent.getStringExtra(EXTRA_EMERGENCY_MESSAGE)
                    ?: "Emergency! I need help."
                val emergencyType = intent.getStringExtra(EXTRA_EMERGENCY_TYPE)
                    ?.let { EmergencyType.valueOf(it) } ?: EmergencyType.GENERAL

                triggerEmergency(message, emergencyType)
            }
            ACTION_CANCEL_EMERGENCY -> {
                cancelEmergency()
            }
        }

        return START_STICKY
    }

    private fun triggerEmergency(message: String, emergencyType: EmergencyType) {
        emergencyJob?.cancel()
        emergencyJob = serviceScope.launch {
            try {
                startForeground(NOTIFICATION_ID, createEmergencyNotification("Triggering emergency alert..."))

                // Step 1: Get current location
                updateNotification("Getting location...")
                val location = locationService.getCurrentLocation()

                if (location == null) {
                    updateNotification("Failed to get location")
                    delay(3000)
                    stopSelf()
                    return@launch
                }

                // Step 2: Get primary emergency contact
                val primaryContact = database.emergencyContactDao().getPrimaryContact()

                if (primaryContact == null) {
                    updateNotification("No emergency contact configured")
                    delay(3000)
                    stopSelf()
                    return@launch
                }

                // Step 3: Create emergency alert record
                val alertId = UUID.randomUUID().toString()
                val alert = EmergencyAlert(
                    id = alertId,
                    location = locationService.formatLocationCoordinates(location),
                    latitude = location.latitude,
                    longitude = location.longitude,
                    message = message,
                    dateTime = Date(),
                    contactCalled = primaryContact.phoneNumber,
                    emergencyType = emergencyType
                )

                // Save to local database
                database.emergencyAlertDao().insertAlert(alert)

                // Step 4: Send SMS
                updateNotification("Sending emergency SMS...")
                val smsSuccess = smsService.sendEmergencySMS(
                    primaryContact.phoneNumber,
                    message,
                    locationService.formatLocationForSMS(location)
                )

                // Update alert with SMS status
                val updatedAlert = alert.copy(smsDelivered = smsSuccess)
                database.emergencyAlertDao().updateAlert(updatedAlert)

                // Step 5: Start stealth recording immediately (priority)
                val settings = database.userSettingsDao().getSettingsSync()
                var audioPath: String? = null
                var videoPath: String? = null

                if (settings?.autoRecordEnabled == true) {
                    // Start stealth recording service immediately
                    startStealthRecording(alertId, settings.recordingDuration)

                    // Also do regular recording as backup
                    updateNotification("Recording emergency media...")
                    val recordingResult = mediaRecordingService.recordAudioAndVideo(
                        settings.recordingDuration
                    )
                    audioPath = recordingResult.first
                    videoPath = recordingResult.second
                }

                // Step 6: Make emergency call (if SMS failed or auto-call enabled)
                if (!smsSuccess || settings?.autoCallEnabled == true) {
                    updateNotification("Making emergency call...")
                    delay(2000) // Brief delay before calling

                    val callSuccess = if (settings?.ttsEnabled == true) {
                        callService.makeEmergencyCallWithMessage(
                            primaryContact.phoneNumber,
                            message
                        )
                    } else {
                        callService.makeEmergencyCall(primaryContact.phoneNumber)
                    }

                    // Update alert with call status
                    val finalAlert = updatedAlert.copy(
                        callMade = callSuccess,
                        audioFilePath = audioPath,
                        videoFilePath = videoPath
                    )
                    database.emergencyAlertDao().updateAlert(finalAlert)
                }

                // Step 7: Upload media to Google Drive (background task)
                if ((audioPath != null || videoPath != null) && settings?.googleDriveEnabled == true) {
                    launch {
                        updateNotification("Uploading media to Google Drive...")
                        val driveUploadSuccess = googleDriveService.uploadEmergencyMedia(
                            alertId,
                            audioPath,
                            videoPath
                        )

                        if (driveUploadSuccess != null) {
                            database.emergencyAlertDao().updateMediaLink(alertId, driveUploadSuccess)
                        }
                    }
                }

                // Step 8: Sync to Supabase (background task)
                launch {
                    // This will be implemented in the repository layer
                    // supabaseRepository.syncAlert(finalAlert)
                }

                updateNotification("Emergency alert completed successfully")
                delay(5000)
                stopSelf()

            } catch (e: Exception) {
                updateNotification("Emergency alert failed: ${e.message}")
                delay(5000)
                stopSelf()
            }
        }
    }

    private fun cancelEmergency() {
        emergencyJob?.cancel()
        callService.stopSpeaking()
        mediaRecordingService.cleanup()
        stopStealthRecording()
        updateNotification("Emergency alert cancelled")
        delay(2000)
        stopSelf()
    }

    /**
     * Start stealth recording service for covert evidence collection
     */
    private fun startStealthRecording(alertId: String, durationSeconds: Int) {
        try {
            val stealthIntent = Intent(this, StealthRecordingService::class.java).apply {
                action = StealthRecordingService.ACTION_START_STEALTH_RECORDING
                putExtra(StealthRecordingService.EXTRA_ALERT_ID, alertId)
                putExtra(StealthRecordingService.EXTRA_RECORDING_DURATION, durationSeconds)
            }
            startService(stealthIntent)
        } catch (e: Exception) {
            // Silent failure - don't compromise emergency alert
        }
    }

    /**
     * Stop stealth recording service
     */
    private fun stopStealthRecording() {
        try {
            val stealthIntent = Intent(this, StealthRecordingService::class.java).apply {
                action = StealthRecordingService.ACTION_STOP_STEALTH_RECORDING
            }
            startService(stealthIntent)
        } catch (e: Exception) {
            // Silent failure
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Emergency Alerts",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for emergency alert operations"
                enableVibration(true)
                setShowBadge(true)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createEmergencyNotification(message: String): Notification {
        val cancelIntent = Intent(this, EmergencyService::class.java).apply {
            action = ACTION_CANCEL_EMERGENCY
        }
        val cancelPendingIntent = PendingIntent.getService(
            this, 0, cancelIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Emergency Alert Active")
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_emergency)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setOngoing(true)
            .addAction(
                R.drawable.ic_cancel,
                "Cancel",
                cancelPendingIntent
            )
            .build()
    }

    private fun updateNotification(message: String) {
        val notification = createEmergencyNotification(message)
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        emergencyJob?.cancel()
        serviceScope.cancel()
        callService.destroy()
        mediaRecordingService.cleanup()
    }

    override fun onBind(intent: Intent?): IBinder? = null
}
