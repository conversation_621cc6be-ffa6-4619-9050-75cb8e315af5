<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.history.HistoryFragment">

    <TextView
        android:id="@+id/historyTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Emergency History"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
        android:textColor="@color/primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/historyRecyclerView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/historyTitle" />

    <TextView
        android:id="@+id/emptyStateText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/history_empty"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="@color/on_surface_variant"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
