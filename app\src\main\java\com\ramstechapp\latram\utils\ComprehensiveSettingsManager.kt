package com.ramstechapp.latram.utils

import android.content.Context
import android.content.Intent
import com.ramstechapp.latram.receiver.PowerButtonReceiver
import com.ramstechapp.latram.service.*

/**
 * Comprehensive Settings Manager
 * Manages all emergency features and settings in one place
 */
class ComprehensiveSettingsManager(private val context: Context) {
    
    private val quickAccessManager = QuickAccessManager(context)
    private val stealthManager = StealthManager(context)
    private val permissionManager = PermissionManager(context)
    
    /**
     * Enable all emergency features
     */
    fun enableAllEmergencyFeatures(): EmergencyFeaturesStatus {
        val results = mutableListOf<String>()
        val issues = mutableListOf<String>()
        
        try {
            // 1. Quick Access Features
            val quickAccessIssues = quickAccessManager.enableAllQuickAccess()
            if (quickAccessIssues.isEmpty()) {
                results.add("Quick access enabled")
            } else {
                issues.addAll(quickAccessIssues)
            }
            
            // 2. Stealth Recording
            if (stealthManager.isStealthRecordingEnabled()) {
                val stealthIssues = stealthManager.configureDeviceForStealth()
                if (stealthIssues.isEmpty()) {
                    results.add("Stealth recording configured")
                } else {
                    issues.addAll(stealthIssues)
                }
            }
            
            // 3. Gesture Detection
            enableGestureDetection()
            results.add("Gesture detection enabled")
            
            // 4. Voice Commands
            enableVoiceCommands()
            results.add("Voice commands enabled")
            
            // 5. Power Button Emergency
            PowerButtonReceiver.enablePowerButtonEmergency(context, true)
            results.add("Power button emergency enabled")
            
        } catch (e: Exception) {
            issues.add("Failed to enable some features: ${e.message}")
        }
        
        return EmergencyFeaturesStatus(
            enabledFeatures = results,
            issues = issues,
            allFeaturesEnabled = issues.isEmpty()
        )
    }
    
    /**
     * Disable all emergency features
     */
    fun disableAllEmergencyFeatures() {
        try {
            // Disable quick access
            quickAccessManager.disableAllQuickAccess()
            
            // Disable gesture detection
            disableGestureDetection()
            
            // Disable voice commands
            disableVoiceCommands()
            
            // Disable power button emergency
            PowerButtonReceiver.enablePowerButtonEmergency(context, false)
            
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    /**
     * Get comprehensive status of all features
     */
    fun getAllFeaturesStatus(): ComprehensiveStatus {
        val quickAccessStatus = quickAccessManager.getQuickAccessStatus()
        val stealthConfig = stealthManager.getStealthConfiguration()
        val permissionStatus = permissionManager.getPermissionStatus()
        
        return ComprehensiveStatus(
            quickAccess = quickAccessStatus,
            stealthRecording = stealthConfig,
            permissions = permissionStatus,
            gestureDetection = isGestureDetectionEnabled(),
            voiceCommands = isVoiceCommandsEnabled(),
            powerButtonEmergency = PowerButtonReceiver.isPowerButtonEmergencyEnabled(context)
        )
    }
    
    /**
     * Configure emergency features based on user preferences
     */
    fun configureEmergencyFeatures(config: EmergencyConfiguration) {
        // Quick Access
        if (config.enableQuickAccess) {
            quickAccessManager.enableAllQuickAccess()
        } else {
            quickAccessManager.disableAllQuickAccess()
        }
        
        // Stealth Recording
        stealthManager.setStealthRecordingEnabled(config.enableStealthRecording)
        stealthManager.setRealTimeUploadEnabled(config.enableRealTimeUpload)
        stealthManager.setSilentRecording(config.enableSilentRecording)
        
        // Gesture Detection
        if (config.enableGestureDetection) {
            enableGestureDetection()
        } else {
            disableGestureDetection()
        }
        
        // Voice Commands
        if (config.enableVoiceCommands) {
            enableVoiceCommands()
        } else {
            disableVoiceCommands()
        }
        
        // Power Button Emergency
        PowerButtonReceiver.enablePowerButtonEmergency(context, config.enablePowerButtonEmergency)
    }
    
    private fun enableGestureDetection() {
        try {
            val intent = Intent(context, GestureDetectionService::class.java).apply {
                action = GestureDetectionService.ACTION_START_GESTURE_DETECTION
            }
            context.startService(intent)
            setGestureDetectionEnabled(true)
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    private fun disableGestureDetection() {
        try {
            val intent = Intent(context, GestureDetectionService::class.java).apply {
                action = GestureDetectionService.ACTION_STOP_GESTURE_DETECTION
            }
            context.startService(intent)
            setGestureDetectionEnabled(false)
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    private fun enableVoiceCommands() {
        try {
            val intent = Intent(context, VoiceCommandService::class.java).apply {
                action = VoiceCommandService.ACTION_START_VOICE_LISTENING
            }
            context.startService(intent)
            setVoiceCommandsEnabled(true)
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    private fun disableVoiceCommands() {
        try {
            val intent = Intent(context, VoiceCommandService::class.java).apply {
                action = VoiceCommandService.ACTION_STOP_VOICE_LISTENING
            }
            context.startService(intent)
            setVoiceCommandsEnabled(false)
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    private fun setGestureDetectionEnabled(enabled: Boolean) {
        val prefs = context.getSharedPreferences("latram_emergency_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("gesture_detection_enabled", enabled).apply()
    }
    
    private fun isGestureDetectionEnabled(): Boolean {
        val prefs = context.getSharedPreferences("latram_emergency_prefs", Context.MODE_PRIVATE)
        return prefs.getBoolean("gesture_detection_enabled", false)
    }
    
    private fun setVoiceCommandsEnabled(enabled: Boolean) {
        val prefs = context.getSharedPreferences("latram_emergency_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean("voice_commands_enabled", enabled).apply()
    }
    
    private fun isVoiceCommandsEnabled(): Boolean {
        val prefs = context.getSharedPreferences("latram_emergency_prefs", Context.MODE_PRIVATE)
        return prefs.getBoolean("voice_commands_enabled", false)
    }
    
    /**
     * Test all emergency features
     */
    fun testAllFeatures(): TestResults {
        val results = mutableMapOf<String, Boolean>()
        
        // Test permissions
        results["Critical Permissions"] = permissionManager.checkCriticalPermissions()
        results["All Permissions"] = permissionManager.checkAllPermissions()
        results["Overlay Permission"] = permissionManager.canDrawOverlays()
        
        // Test services
        results["Quick Access"] = quickAccessManager.getQuickAccessStatus().isFullyEnabled
        results["Stealth Recording"] = stealthManager.isStealthRecordingEnabled()
        results["Gesture Detection"] = isGestureDetectionEnabled()
        results["Voice Commands"] = isVoiceCommandsEnabled()
        results["Power Button Emergency"] = PowerButtonReceiver.isPowerButtonEmergencyEnabled(context)
        
        return TestResults(
            results = results,
            allPassed = results.values.all { it },
            passedCount = results.values.count { it },
            totalCount = results.size
        )
    }
}

data class EmergencyFeaturesStatus(
    val enabledFeatures: List<String>,
    val issues: List<String>,
    val allFeaturesEnabled: Boolean
)

data class ComprehensiveStatus(
    val quickAccess: QuickAccessStatus,
    val stealthRecording: StealthConfiguration,
    val permissions: PermissionStatus,
    val gestureDetection: Boolean,
    val voiceCommands: Boolean,
    val powerButtonEmergency: Boolean
)

data class EmergencyConfiguration(
    val enableQuickAccess: Boolean = true,
    val enableStealthRecording: Boolean = true,
    val enableRealTimeUpload: Boolean = true,
    val enableSilentRecording: Boolean = true,
    val enableGestureDetection: Boolean = true,
    val enableVoiceCommands: Boolean = true,
    val enablePowerButtonEmergency: Boolean = true
)

data class TestResults(
    val results: Map<String, Boolean>,
    val allPassed: Boolean,
    val passedCount: Int,
    val totalCount: Int
)
