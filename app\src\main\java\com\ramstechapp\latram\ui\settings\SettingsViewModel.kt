package com.ramstechapp.latram.ui.settings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ramstechapp.latram.LatramApplication
import com.ramstechapp.latram.data.model.UserSettings
import com.ramstechapp.latram.utils.Event
import kotlinx.coroutines.launch

class SettingsViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = (application as LatramApplication).emergencyRepository

    val settings = repository.getUserSettings()

    private val _showToast = MutableLiveData<Event<String>>()
    val showToast: LiveData<Event<String>> = _showToast

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    fun loadSettings() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // Settings are automatically loaded via Flow from repository
            } catch (e: Exception) {
                _showToast.value = Event("Failed to load settings: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun saveSettings(settings: UserSettings) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                repository.insertUserSettings(settings)
                _showToast.value = Event("Settings saved successfully")

            } catch (e: Exception) {
                _showToast.value = Event("Failed to save settings: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }
}
