package com.ramstechapp.latram.ui.settings

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.ramstechapp.latram.R
import com.ramstechapp.latram.databinding.FragmentSettingsBinding
import com.ramstechapp.latram.utils.ComprehensiveSettingsManager

class SettingsFragment : Fragment() {

    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!

    private lateinit var settingsViewModel: SettingsViewModel
    private lateinit var comprehensiveSettingsManager: ComprehensiveSettingsManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        settingsViewModel = ViewModelProvider(this)[SettingsViewModel::class.java]
        comprehensiveSettingsManager = ComprehensiveSettingsManager(requireContext())

        _binding = FragmentSettingsBinding.inflate(inflater, container, false)

        setupClickListeners()
        observeViewModel()
        loadCurrentSettings()

        return binding.root
    }

    private fun setupClickListeners() {
        binding.apply {
            // Emergency Contacts
            manageContactsButton.setOnClickListener {
                findNavController().navigate(R.id.navigation_contacts)
            }

            // Save Settings
            saveSettingsButton.setOnClickListener {
                saveSettings()
            }

            // Test Emergency Features
            testFeaturesButton.setOnClickListener {
                testEmergencyFeatures()
            }

            // Enable All Features
            enableAllFeaturesButton.setOnClickListener {
                enableAllEmergencyFeatures()
            }
        }
    }

    private fun observeViewModel() {
        settingsViewModel.settings.observe(viewLifecycleOwner) { settings ->
            settings?.let { updateUI(it) }
        }

        settingsViewModel.showToast.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { message ->
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun loadCurrentSettings() {
        settingsViewModel.loadSettings()
    }

    private fun updateUI(settings: com.ramstechapp.latram.data.model.UserSettings) {
        binding.apply {
            defaultMessageInput.setText(settings.defaultMessage)
            autoCallSwitch.isChecked = settings.autoCallEnabled
            autoRecordSwitch.isChecked = settings.autoRecordEnabled
            googleDriveSwitch.isChecked = settings.googleDriveEnabled
            ttsSwitch.isChecked = settings.ttsEnabled
            stealthRecordingSwitch.isChecked = settings.stealthRecordingEnabled
            realTimeUploadSwitch.isChecked = settings.realTimeUploadEnabled
            silentRecordingSwitch.isChecked = settings.silentRecordingEnabled
        }
    }

    private fun saveSettings() {
        val settings = com.ramstechapp.latram.data.model.UserSettings(
            defaultMessage = binding.defaultMessageInput.text.toString().trim(),
            autoCallEnabled = binding.autoCallSwitch.isChecked,
            autoRecordEnabled = binding.autoRecordSwitch.isChecked,
            googleDriveEnabled = binding.googleDriveSwitch.isChecked,
            ttsEnabled = binding.ttsSwitch.isChecked,
            stealthRecordingEnabled = binding.stealthRecordingSwitch.isChecked,
            realTimeUploadEnabled = binding.realTimeUploadSwitch.isChecked,
            silentRecordingEnabled = binding.silentRecordingSwitch.isChecked
        )

        settingsViewModel.saveSettings(settings)
    }

    private fun testEmergencyFeatures() {
        val testResults = comprehensiveSettingsManager.testAllFeatures()

        val message = buildString {
            append("Test Results: ${testResults.passedCount}/${testResults.totalCount} passed\n\n")
            testResults.results.forEach { (feature, passed) ->
                append("${if (passed) "✅" else "❌"} $feature\n")
            }
        }

        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("Emergency Features Test")
            .setMessage(message)
            .setPositiveButton("OK", null)
            .show()
    }

    private fun enableAllEmergencyFeatures() {
        val result = comprehensiveSettingsManager.enableAllEmergencyFeatures()

        val message = buildString {
            if (result.allFeaturesEnabled) {
                append("✅ All emergency features enabled successfully!\n\n")
                append("Enabled features:\n")
                result.enabledFeatures.forEach { feature ->
                    append("• $feature\n")
                }
            } else {
                append("⚠️ Some features need attention:\n\n")
                result.issues.forEach { issue ->
                    append("• $issue\n")
                }
            }
        }

        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("Emergency Features Setup")
            .setMessage(message)
            .setPositiveButton("OK", null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
