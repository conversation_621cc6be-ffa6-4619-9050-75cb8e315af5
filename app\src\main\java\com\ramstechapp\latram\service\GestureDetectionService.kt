package com.ramstechapp.latram.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.IBinder
import android.os.PowerManager
import android.os.Vibrator
import kotlinx.coroutines.*
import kotlin.math.sqrt

/**
 * Gesture Detection Service
 * Detects emergency gestures like:
 * - Shake phone rapidly 5 times
 * - Press power button 5 times quickly
 * - Volume up + down simultaneously 3 times
 */
class GestureDetectionService : Service(), SensorEventListener {
    
    companion object {
        const val ACTION_START_GESTURE_DETECTION = "com.ramstechapp.latram.START_GESTURE_DETECTION"
        const val ACTION_STOP_GESTURE_DETECTION = "com.ramstechapp.latram.STOP_GESTURE_DETECTION"
        
        // Shake detection constants
        private const val SHAKE_THRESHOLD = 12.0f
        private const val SHAKE_COUNT_REQUIRED = 5
        private const val SHAKE_TIME_WINDOW = 3000L // 3 seconds
    }
    
    private var sensorManager: SensorManager? = null
    private var accelerometer: Sensor? = null
    private var isDetecting = false
    
    // Shake detection variables
    private var lastShakeTime = 0L
    private var shakeCount = 0
    private val shakeTimestamps = mutableListOf<Long>()
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    override fun onCreate() {
        super.onCreate()
        sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
        accelerometer = sensorManager?.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_GESTURE_DETECTION -> {
                startGestureDetection()
            }
            ACTION_STOP_GESTURE_DETECTION -> {
                stopGestureDetection()
                stopSelf()
            }
        }
        return START_STICKY
    }
    
    private fun startGestureDetection() {
        if (isDetecting) return
        
        accelerometer?.let { sensor ->
            sensorManager?.registerListener(this, sensor, SensorManager.SENSOR_DELAY_UI)
            isDetecting = true
        }
    }
    
    private fun stopGestureDetection() {
        if (!isDetecting) return
        
        sensorManager?.unregisterListener(this)
        isDetecting = false
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        if (event?.sensor?.type == Sensor.TYPE_ACCELEROMETER) {
            detectShakeGesture(event)
        }
    }
    
    private fun detectShakeGesture(event: SensorEvent) {
        val x = event.values[0]
        val y = event.values[1]
        val z = event.values[2]
        
        // Calculate acceleration magnitude
        val acceleration = sqrt(x * x + y * y + z * z) - SensorManager.GRAVITY_EARTH
        
        if (acceleration > SHAKE_THRESHOLD) {
            val currentTime = System.currentTimeMillis()
            
            // Check if this shake is within time window of previous shakes
            if (currentTime - lastShakeTime > 500) { // Minimum 500ms between shakes
                lastShakeTime = currentTime
                shakeTimestamps.add(currentTime)
                
                // Remove old timestamps outside time window
                shakeTimestamps.removeAll { it < currentTime - SHAKE_TIME_WINDOW }
                
                // Check if we have enough shakes in time window
                if (shakeTimestamps.size >= SHAKE_COUNT_REQUIRED) {
                    onEmergencyGestureDetected("SHAKE_GESTURE")
                    shakeTimestamps.clear()
                }
            }
        }
    }
    
    private fun onEmergencyGestureDetected(gestureType: String) {
        serviceScope.launch {
            try {
                // Provide haptic feedback
                val vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
                val pattern = longArrayOf(0, 100, 100, 100, 100, 100) // SOS pattern
                vibrator.vibrate(pattern, -1)
                
                // Wake up screen
                val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
                val wakeLock = powerManager.newWakeLock(
                    PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
                    "LATRAM:GestureWakeup"
                )
                wakeLock.acquire(5000) // 5 seconds
                
                // Trigger emergency
                val emergencyIntent = Intent(this@GestureDetectionService, EmergencyService::class.java).apply {
                    action = EmergencyService.ACTION_TRIGGER_EMERGENCY
                    putExtra(EmergencyService.EXTRA_EMERGENCY_MESSAGE, "Emergency triggered by gesture: $gestureType")
                    putExtra(EmergencyService.EXTRA_EMERGENCY_TYPE, "GENERAL")
                }
                startForegroundService(emergencyIntent)
                
                wakeLock.release()
                
            } catch (e: Exception) {
                // Silent failure
            }
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // Not needed for this implementation
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopGestureDetection()
        serviceScope.cancel()
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
}
