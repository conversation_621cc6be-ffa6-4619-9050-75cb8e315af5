package com.ramstechapp.latram.ui.auth

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ramstechapp.latram.LatramApplication
import com.ramstechapp.latram.data.model.*
import kotlinx.coroutines.launch

class AuthViewModel(application: Application) : AndroidViewModel(application) {

    private val authRepository = (application as LatramApplication).authRepository

    private val _authState = MutableLiveData<AuthState>()
    val authState: LiveData<AuthState> = _authState

    private val _forgotPasswordState = MutableLiveData<ForgotPasswordState>()
    val forgotPasswordState: LiveData<ForgotPasswordState> = _forgotPasswordState

    val currentUser = authRepository.getCurrentUser()

    init {
        // Initialize auth state
        _authState.value = if (authRepository.isLoggedIn()) {
            AuthState.Authenticated
        } else {
            AuthState.Unauthenticated
        }
        _forgotPasswordState.value = ForgotPasswordState.Idle
    }

    fun login(request: LoginRequest) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            
            try {
                val response = authRepository.login(request)
                
                if (response.success) {
                    _authState.value = AuthState.Authenticated
                } else {
                    _authState.value = AuthState.Error(response.message)
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Login failed: ${e.message}")
            }
        }
    }

    fun register(request: RegisterRequest) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            
            try {
                val response = authRepository.register(request)
                
                if (response.success) {
                    _authState.value = AuthState.Authenticated
                } else {
                    _authState.value = AuthState.Error(response.message)
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Registration failed: ${e.message}")
            }
        }
    }

    fun logout() {
        viewModelScope.launch {
            try {
                authRepository.logout()
                _authState.value = AuthState.Unauthenticated
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Logout failed: ${e.message}")
            }
        }
    }

    fun forgotPassword(email: String) {
        viewModelScope.launch {
            _forgotPasswordState.value = ForgotPasswordState.Loading
            
            try {
                val success = authRepository.forgotPassword(email)
                
                if (success) {
                    _forgotPasswordState.value = ForgotPasswordState.Success
                } else {
                    _forgotPasswordState.value = ForgotPasswordState.Error("Failed to send reset email")
                }
            } catch (e: Exception) {
                _forgotPasswordState.value = ForgotPasswordState.Error("Error: ${e.message}")
            }
        }
    }

    fun resetPassword(request: ResetPasswordRequest) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            
            try {
                val response = authRepository.resetPassword(request)
                
                if (response.success) {
                    _authState.value = AuthState.Authenticated
                } else {
                    _authState.value = AuthState.Error(response.message)
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Password reset failed: ${e.message}")
            }
        }
    }

    fun verifyEmail(email: String, code: String) {
        viewModelScope.launch {
            try {
                val success = authRepository.verifyEmail(email, code)
                
                if (success) {
                    // Email verified successfully
                    // Current user will be updated automatically via Flow
                } else {
                    _authState.value = AuthState.Error("Email verification failed")
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Verification error: ${e.message}")
            }
        }
    }

    fun resendVerification(email: String) {
        viewModelScope.launch {
            try {
                val success = authRepository.resendVerification(email)
                
                if (!success) {
                    _authState.value = AuthState.Error("Failed to resend verification email")
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Error: ${e.message}")
            }
        }
    }

    fun updateProfile(user: User) {
        viewModelScope.launch {
            try {
                val success = authRepository.updateProfile(user)
                
                if (!success) {
                    _authState.value = AuthState.Error("Failed to update profile")
                }
            } catch (e: Exception) {
                _authState.value = AuthState.Error("Update error: ${e.message}")
            }
        }
    }

    fun isLoggedIn(): Boolean = authRepository.isLoggedIn()

    fun getCurrentUserId(): String? = authRepository.getCurrentUserId()

    fun getAccessToken(): String? = authRepository.getAccessToken()
}

// Auth States
sealed class AuthState {
    object Loading : AuthState()
    object Authenticated : AuthState()
    object Unauthenticated : AuthState()
    data class Error(val message: String) : AuthState()
}

sealed class ForgotPasswordState {
    object Idle : ForgotPasswordState()
    object Loading : ForgotPasswordState()
    object Success : ForgotPasswordState()
    data class Error(val message: String) : ForgotPasswordState()
}
