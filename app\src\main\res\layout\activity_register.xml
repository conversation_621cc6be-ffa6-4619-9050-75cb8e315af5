<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.auth.RegisterActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/on_surface" />

        <!-- Logo -->
        <ImageView
            android:id="@+id/registerLogo"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="16dp"
            android:src="@drawable/ic_emergency"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/backButton"
            app:tint="@color/primary" />

        <!-- Title -->
        <TextView
            android:id="@+id/registerTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Create Account"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
            android:textColor="@color/on_surface"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/registerLogo" />

        <!-- Subtitle -->
        <TextView
            android:id="@+id/registerSubtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="Join LATRAM for complete emergency protection"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="@color/on_surface_variant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/registerTitle" />

        <!-- Full Name Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/fullNameInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:hint="Full Name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/registerSubtitle"
            app:startIconDrawable="@drawable/ic_person">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/fullNameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Email Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/emailInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Email Address"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fullNameInputLayout"
            app:startIconDrawable="@drawable/ic_email">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/emailInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Phone Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/phoneInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Phone Number (Optional)"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/emailInputLayout"
            app:startIconDrawable="@drawable/ic_phone">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/phoneInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="phone" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/passwordInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Password"
            app:endIconMode="password_toggle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/phoneInputLayout"
            app:startIconDrawable="@drawable/ic_lock">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/passwordInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Confirm Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/confirmPasswordInputLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Confirm Password"
            app:endIconMode="password_toggle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/passwordInputLayout"
            app:startIconDrawable="@drawable/ic_lock">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/confirmPasswordInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Terms Checkbox -->
        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/termsCheckbox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="I agree to the Terms of Service and Privacy Policy"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/confirmPasswordInputLayout" />

        <!-- Register Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/registerButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginTop="24dp"
            android:text="Create Account"
            android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
            android:enabled="false"
            app:cornerRadius="28dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/termsCheckbox" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/registerButton"
            app:layout_constraintEnd_toEndOf="@+id/registerButton"
            app:layout_constraintStart_toStartOf="@+id/registerButton"
            app:layout_constraintTop_toTopOf="@+id/registerButton" />

        <!-- Login Link -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/registerButton">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Already have an account? "
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/on_surface_variant" />

            <TextView
                android:id="@+id/loginText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sign In"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/primary"
                android:textStyle="bold"
                android:background="?attr/selectableItemBackground"
                android:padding="4dp" />

        </LinearLayout>

        <!-- Emergency Access -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/emergencyAccessButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="24dp"
            android:text="🚨 Emergency Access"
            android:textColor="@color/emergency_red"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/loginText" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
