package com.ramstechapp.latram.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProvider
import com.ramstechapp.latram.R
import com.ramstechapp.latram.data.model.EmergencyType
import com.ramstechapp.latram.databinding.FragmentHomeBinding
import com.ramstechapp.latram.ui.viewmodel.MainViewModel

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var homeViewModel: HomeViewModel
    private val mainViewModel: MainViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        homeViewModel = ViewModelProvider(this)[HomeViewModel::class.java]
        
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()
        observeViewModels()
        
        return root
    }
    
    private fun setupUI() {
        // Setup emergency type dropdown
        val emergencyTypes = arrayOf(
            "General Emergency",
            "Medical Emergency", 
            "Fire Emergency",
            "Police Emergency",
            "Accident",
            "Natural Disaster"
        )
        
        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            emergencyTypes
        )
        binding.emergencyTypeSpinner.setAdapter(adapter)
        binding.emergencyTypeSpinner.setText(emergencyTypes[0], false)
        
        // Setup emergency button click
        binding.emergencyButton.setOnClickListener {
            triggerEmergency()
        }
        
        // Setup test buttons
        binding.testLocationButton.setOnClickListener {
            homeViewModel.testLocation()
        }
        
        binding.viewContactsButton.setOnClickListener {
            // Navigate to contacts or settings
            Toast.makeText(context, "Navigate to contacts", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun observeViewModels() {
        homeViewModel.statusText.observe(viewLifecycleOwner) { status ->
            binding.statusText.text = status
        }
        
        homeViewModel.lastAlertText.observe(viewLifecycleOwner) { lastAlert ->
            binding.lastAlertText.text = lastAlert
        }
        
        homeViewModel.showToast.observe(viewLifecycleOwner) { event ->
            event.getContentIfNotHandled()?.let { message ->
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            }
        }
        
        mainViewModel.isEmergencyActive.observe(viewLifecycleOwner) { isActive ->
            binding.emergencyButton.isEnabled = !isActive
            if (isActive) {
                binding.statusText.text = "Emergency alert in progress..."
            }
        }
    }
    
    private fun triggerEmergency() {
        val selectedType = when (binding.emergencyTypeSpinner.text.toString()) {
            "Medical Emergency" -> EmergencyType.MEDICAL
            "Fire Emergency" -> EmergencyType.FIRE
            "Police Emergency" -> EmergencyType.POLICE
            "Accident" -> EmergencyType.ACCIDENT
            "Natural Disaster" -> EmergencyType.NATURAL_DISASTER
            else -> EmergencyType.GENERAL
        }
        
        val customMessage = binding.customMessageInput.text?.toString()?.trim() ?: ""
        
        mainViewModel.triggerEmergency(customMessage, selectedType)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
