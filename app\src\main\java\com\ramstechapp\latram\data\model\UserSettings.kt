package com.ramstechapp.latram.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

@Entity(tableName = "user_settings")
data class UserSettings(
    @PrimaryKey
    val id: String = "default_settings",
    
    // Basic Emergency Settings
    val defaultMessage: String = "Emergency! I need help. Please check my location.",
    val autoCallEnabled: Boolean = true,
    val autoRecordEnabled: Boolean = true,
    val googleDriveEnabled: Boolean = false,
    val ttsEnabled: Boolean = true,
    val recordingDuration: Int = 60, // seconds
    
    // Stealth Recording Settings
    val stealthRecordingEnabled: Boolean = true,
    val realTimeUploadEnabled: Boolean = true,
    val silentRecordingEnabled: Boolean = true,
    val autoDeleteAfterUpload: Boolean = false,
    
    // Quick Access Settings
    val floatingButtonEnabled: Boolean = true,
    val lockScreenAccessEnabled: Boolean = true,
    val gestureDetectionEnabled: Boolean = true,
    val voiceCommandsEnabled: Boolean = true,
    val powerButtonEmergencyEnabled: Boolean = true,
    
    // Social Media Settings
    val whatsappSharingEnabled: Boolean = true,
    val telegramSharingEnabled: Boolean = true,
    val socialMediaBroadcastEnabled: Boolean = false,
    
    // Backup Settings
    val autoBackupEnabled: Boolean = true,
    val backupToGoogleDrive: Boolean = true,
    val backupToSupabase: Boolean = true,
    
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)
