package com.ramstechapp.latram.utils

import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.ramstechapp.latram.R

class StealthIndicator(private val context: Context) {
    
    private var windowManager: WindowManager? = null
    private var indicatorView: View? = null
    private var isVisible = false
    
    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }
    
    /**
     * Show minimal stealth recording indicator
     * Very small, barely visible dot in corner
     */
    fun showStealthIndicator() {
        if (isVisible || !canDrawOverlays()) return
        
        try {
            // Create minimal indicator (tiny red dot)
            indicatorView = ImageView(context).apply {
                setImageResource(R.drawable.stealth_indicator)
                alpha = 0.3f // Very transparent
            }
            
            val layoutParams = WindowManager.LayoutParams().apply {
                width = 8 // Very small - 8dp
                height = 8
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
                x = 10 // Small offset from edge
                y = 10
            }
            
            windowManager?.addView(indicatorView, layoutParams)
            isVisible = true
            
            // Auto-hide after 3 seconds for maximum stealth
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                hideStealthIndicator()
            }, 3000)
            
        } catch (e: Exception) {
            // Silent failure for stealth
        }
    }
    
    /**
     * Hide stealth indicator
     */
    fun hideStealthIndicator() {
        if (!isVisible || indicatorView == null) return
        
        try {
            windowManager?.removeView(indicatorView)
            isVisible = false
            indicatorView = null
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    /**
     * Show brief flash to indicate recording started
     * Very subtle - just a quick fade in/out
     */
    fun showRecordingStartFlash() {
        if (!canDrawOverlays()) return
        
        try {
            val flashView = View(context).apply {
                setBackgroundColor(ContextCompat.getColor(context, R.color.emergency_red))
                alpha = 0f
            }
            
            val layoutParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.MATCH_PARENT
                height = WindowManager.LayoutParams.MATCH_PARENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
            }
            
            windowManager?.addView(flashView, layoutParams)
            
            // Quick fade in/out animation
            flashView.animate()
                .alpha(0.1f)
                .setDuration(100)
                .withEndAction {
                    flashView.animate()
                        .alpha(0f)
                        .setDuration(200)
                        .withEndAction {
                            try {
                                windowManager?.removeView(flashView)
                            } catch (e: Exception) {
                                // Silent failure
                            }
                        }
                }
            
        } catch (e: Exception) {
            // Silent failure for stealth
        }
    }
    
    /**
     * Show upload progress indicator (very minimal)
     */
    fun showUploadProgress(progress: Float) {
        // Only show if progress is significant to avoid detection
        if (progress < 0.5f || !canDrawOverlays()) return
        
        try {
            // Very small progress indicator in corner
            // Implementation would be similar to stealth indicator
            // but with progress visualization
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    /**
     * Vibrate device subtly to indicate recording status
     */
    fun subtleVibration(pattern: VibrationPattern) {
        try {
            val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as android.os.VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                context.getSystemService(Context.VIBRATOR_SERVICE) as android.os.Vibrator
            }
            
            when (pattern) {
                VibrationPattern.RECORDING_START -> {
                    // Very brief single vibration
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        vibrator.vibrate(android.os.VibrationEffect.createOneShot(50, 50))
                    } else {
                        @Suppress("DEPRECATION")
                        vibrator.vibrate(50)
                    }
                }
                VibrationPattern.UPLOAD_COMPLETE -> {
                    // Two very brief vibrations
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        vibrator.vibrate(android.os.VibrationEffect.createWaveform(longArrayOf(0, 30, 100, 30), -1))
                    } else {
                        @Suppress("DEPRECATION")
                        vibrator.vibrate(longArrayOf(0, 30, 100, 30), -1)
                    }
                }
                VibrationPattern.ERROR -> {
                    // Three very brief vibrations
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        vibrator.vibrate(android.os.VibrationEffect.createWaveform(longArrayOf(0, 20, 50, 20, 50, 20), -1))
                    } else {
                        @Suppress("DEPRECATION")
                        vibrator.vibrate(longArrayOf(0, 20, 50, 20, 50, 20), -1)
                    }
                }
            }
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    private fun canDrawOverlays(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            android.provider.Settings.canDrawOverlays(context)
        } else {
            true
        }
    }
    
    fun cleanup() {
        hideStealthIndicator()
    }
}

enum class VibrationPattern {
    RECORDING_START,
    UPLOAD_COMPLETE,
    ERROR
}
