package com.ramstechapp.latram.ui.history

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.ramstechapp.latram.databinding.FragmentHistoryBinding

class HistoryFragment : Fragment() {

    private var _binding: FragmentHistoryBinding? = null
    private val binding get() = _binding!!

    private lateinit var historyViewModel: HistoryViewModel
    private lateinit var alertsAdapter: EmergencyAlertsAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        historyViewModel = ViewModelProvider(this)[HistoryViewModel::class.java]
        _binding = FragmentHistoryBinding.inflate(inflater, container, false)

        setupRecyclerView()
        observeViewModel()

        return binding.root
    }

    private fun setupRecyclerView() {
        alertsAdapter = EmergencyAlertsAdapter { alert ->
            // Handle alert click - show details
            showAlertDetails(alert)
        }

        binding.historyRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = alertsAdapter
        }
    }

    private fun observeViewModel() {
        historyViewModel.alerts.observe(viewLifecycleOwner) { alerts ->
            alertsAdapter.submitList(alerts)
            updateEmptyState(alerts.isEmpty())
        }
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            binding.emptyStateText.visibility = View.VISIBLE
            binding.historyRecyclerView.visibility = View.GONE
        } else {
            binding.emptyStateText.visibility = View.GONE
            binding.historyRecyclerView.visibility = View.VISIBLE
        }
    }

    private fun showAlertDetails(alert: com.ramstechapp.latram.data.model.EmergencyAlert) {
        // TODO: Show alert details dialog or navigate to details screen
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
