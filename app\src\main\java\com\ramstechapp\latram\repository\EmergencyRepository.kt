package com.ramstechapp.latram.repository

import androidx.lifecycle.LiveData
import com.ramstechapp.latram.data.database.LatramDatabase
import com.ramstechapp.latram.data.model.EmergencyAlert
import com.ramstechapp.latram.data.model.EmergencyContact
import com.ramstechapp.latram.data.model.EmergencyType
import com.ramstechapp.latram.data.model.UserSettings
import com.ramstechapp.latram.data.remote.api.SupabaseApiService
import com.ramstechapp.latram.data.remote.dto.toCreateAlertRequest
import com.ramstechapp.latram.data.remote.dto.toEmergencyAlert
import com.ramstechapp.latram.data.remote.SupabaseConfig
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

class EmergencyRepository(
    private val database: LatramDatabase,
    private val supabaseApi: SupabaseApiService
) {

    // Emergency Alerts
    fun getAllAlerts(): Flow<List<EmergencyAlert>> {
        return database.emergencyAlertDao().getAllAlerts()
    }

    fun getAllAlertsLiveData(): LiveData<List<EmergencyAlert>> {
        return database.emergencyAlertDao().getAllAlertsLiveData()
    }

    suspend fun getAlertById(alertId: String): EmergencyAlert? {
        return database.emergencyAlertDao().getAlertById(alertId)
    }

    suspend fun insertAlert(alert: EmergencyAlert) {
        database.emergencyAlertDao().insertAlert(alert)
    }

    suspend fun updateAlert(alert: EmergencyAlert) {
        database.emergencyAlertDao().updateAlert(alert)
    }

    suspend fun deleteAlert(alert: EmergencyAlert) {
        database.emergencyAlertDao().deleteAlert(alert)
    }

    suspend fun syncAlertsToSupabase(): Boolean = withContext(Dispatchers.IO) {
        try {
            val unsyncedAlerts = database.emergencyAlertDao().getUnsyncedAlerts()

            for (alert in unsyncedAlerts) {
                val response = supabaseApi.createAlert(
                    apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                    authorization = "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}",
                    alert = alert.toCreateAlertRequest()
                )

                if (response.isSuccessful) {
                    database.emergencyAlertDao().markAsSynced(alert.id)
                }
            }
            true
        } catch (e: Exception) {
            false
        }
    }

    suspend fun fetchAlertsFromSupabase(): List<EmergencyAlert>? = withContext(Dispatchers.IO) {
        try {
            val response = supabaseApi.getAllAlerts(
                apiKey = SupabaseConfig.SUPABASE_ANON_KEY,
                authorization = "Bearer ${SupabaseConfig.SUPABASE_ANON_KEY}"
            )

            if (response.isSuccessful) {
                response.body()?.map { it.toEmergencyAlert() }
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    // Emergency Contacts
    fun getAllActiveContacts(): Flow<List<EmergencyContact>> {
        return database.emergencyContactDao().getAllActiveContacts()
    }

    fun getAllActiveContactsLiveData(): LiveData<List<EmergencyContact>> {
        return database.emergencyContactDao().getAllActiveContactsLiveData()
    }

    suspend fun getPrimaryContact(): EmergencyContact? {
        return database.emergencyContactDao().getPrimaryContact()
    }

    suspend fun insertContact(contact: EmergencyContact) {
        database.emergencyContactDao().insertContact(contact)
    }

    suspend fun updateContact(contact: EmergencyContact) {
        database.emergencyContactDao().updateContact(contact)
    }

    suspend fun setPrimaryContact(contactId: String) {
        database.emergencyContactDao().clearAllPrimaryFlags()
        database.emergencyContactDao().setPrimaryContact(contactId)
    }

    suspend fun deleteContact(contact: EmergencyContact) {
        database.emergencyContactDao().deleteContact(contact)
    }

    suspend fun getActiveContactsCount(): Int {
        return database.emergencyContactDao().getActiveContactsCount()
    }

    suspend fun clearPrimaryContacts() {
        database.emergencyContactDao().clearAllPrimaryFlags()
    }

    fun getAllContacts(): Flow<List<EmergencyContact>> {
        return database.emergencyContactDao().getAllActiveContacts()
    }

    // User Settings
    fun getUserSettings(): Flow<UserSettings?> {
        return database.userSettingsDao().getSettings()
    }

    fun getSettings(): Flow<UserSettings?> {
        return database.userSettingsDao().getSettings()
    }

    suspend fun insertUserSettings(settings: UserSettings) {
        database.userSettingsDao().insertSettings(settings)
    }

    fun getSettingsLiveData(): LiveData<UserSettings?> {
        return database.userSettingsDao().getSettingsLiveData()
    }

    suspend fun getSettingsSync(): UserSettings? {
        return database.userSettingsDao().getSettingsSync()
    }

    suspend fun insertOrUpdateSettings(settings: UserSettings) {
        database.userSettingsDao().insertSettings(settings)
    }

    suspend fun updateDefaultMessage(message: String) {
        database.userSettingsDao().updateDefaultMessage(message)
    }

    suspend fun updateAutoCallEnabled(enabled: Boolean) {
        database.userSettingsDao().updateAutoCallEnabled(enabled)
    }

    suspend fun updateAutoRecordEnabled(enabled: Boolean) {
        database.userSettingsDao().updateAutoRecordEnabled(enabled)
    }

    suspend fun updateGoogleDriveEnabled(enabled: Boolean) {
        database.userSettingsDao().updateGoogleDriveEnabled(enabled)
    }

    suspend fun updateTtsEnabled(enabled: Boolean) {
        database.userSettingsDao().updateTtsEnabled(enabled)
    }

    // Statistics and Analytics
    suspend fun getAlertsCount(): Int {
        return database.emergencyAlertDao().getAlertsCount()
    }

    suspend fun getUnsyncedAlertsCount(): Int {
        return database.emergencyAlertDao().getUnsyncedAlertsCount()
    }

    fun getAlertsByType(type: EmergencyType): Flow<List<EmergencyAlert>> {
        return database.emergencyAlertDao().getAlertsByType(type)
    }

    fun getAlertsByDateRange(startDate: Date, endDate: Date): Flow<List<EmergencyAlert>> {
        return database.emergencyAlertDao().getAlertsByDateRange(startDate.time, endDate.time)
    }

    // Cleanup operations
    suspend fun deleteOldAlerts(cutoffDate: Date) {
        database.emergencyAlertDao().deleteOldAlerts(cutoffDate.time)
    }

    suspend fun deleteAllAlerts() {
        database.emergencyAlertDao().deleteAllAlerts()
    }

    suspend fun deleteAllContacts() {
        database.emergencyContactDao().deleteAllContacts()
    }
}
