package com.ramstechapp.latram.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import com.ramstechapp.latram.service.EmergencyService
import kotlinx.coroutines.*

/**
 * Power Button Emergency Receiver
 * Detects rapid power button presses (5 times in 10 seconds)
 * to trigger emergency alert
 */
class PowerButtonReceiver : BroadcastReceiver() {
    
    companion object {
        private const val PREFS_NAME = "power_button_emergency"
        private const val KEY_PRESS_TIMESTAMPS = "press_timestamps"
        private const val REQUIRED_PRESSES = 5
        private const val TIME_WINDOW = 10000L // 10 seconds
        private const val MIN_PRESS_INTERVAL = 200L // Minimum 200ms between presses
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_SCREEN_OFF -> {
                // Screen turned off - could be power button press
                handlePowerButtonPress(context)
            }
            Intent.ACTION_SCREEN_ON -> {
                // Screen turned on - reset if too much time passed
                // This helps distinguish between power button and other wake events
            }
        }
    }
    
    private fun handlePowerButtonPress(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        val currentTime = System.currentTimeMillis()
        
        // Get previous press timestamps
        val timestampsString = prefs.getString(KEY_PRESS_TIMESTAMPS, "")
        val timestamps = if (timestampsString.isNullOrEmpty()) {
            mutableListOf<Long>()
        } else {
            timestampsString.split(",").mapNotNull { it.toLongOrNull() }.toMutableList()
        }
        
        // Remove old timestamps outside time window
        timestamps.removeAll { it < currentTime - TIME_WINDOW }
        
        // Check if this press is too soon after last press (debounce)
        if (timestamps.isNotEmpty() && currentTime - timestamps.last() < MIN_PRESS_INTERVAL) {
            return
        }
        
        // Add current timestamp
        timestamps.add(currentTime)
        
        // Save updated timestamps
        val updatedTimestampsString = timestamps.joinToString(",")
        prefs.edit().putString(KEY_PRESS_TIMESTAMPS, updatedTimestampsString).apply()
        
        // Check if we have enough presses in time window
        if (timestamps.size >= REQUIRED_PRESSES) {
            triggerEmergency(context)
            // Clear timestamps after triggering
            prefs.edit().remove(KEY_PRESS_TIMESTAMPS).apply()
        }
    }
    
    private fun triggerEmergency(context: Context) {
        try {
            // Trigger emergency service
            val emergencyIntent = Intent(context, EmergencyService::class.java).apply {
                action = EmergencyService.ACTION_TRIGGER_EMERGENCY
                putExtra(EmergencyService.EXTRA_EMERGENCY_MESSAGE, 
                    "Emergency triggered by power button (${REQUIRED_PRESSES} rapid presses)")
                putExtra(EmergencyService.EXTRA_EMERGENCY_TYPE, "GENERAL")
            }
            context.startForegroundService(emergencyIntent)
            
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    /**
     * Enable power button emergency detection
     */
    companion object {
        fun enablePowerButtonEmergency(context: Context, enabled: Boolean) {
            val prefs = context.getSharedPreferences("latram_emergency_prefs", Context.MODE_PRIVATE)
            prefs.edit().putBoolean("power_button_emergency_enabled", enabled).apply()
        }
        
        fun isPowerButtonEmergencyEnabled(context: Context): Boolean {
            val prefs = context.getSharedPreferences("latram_emergency_prefs", Context.MODE_PRIVATE)
            return prefs.getBoolean("power_button_emergency_enabled", false)
        }
    }
}
