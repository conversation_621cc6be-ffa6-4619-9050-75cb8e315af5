package com.ramstechapp.latram.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.ramstechapp.latram.data.model.EmergencyContact
import kotlinx.coroutines.flow.Flow

@Dao
interface EmergencyContactDao {
    
    @Query("SELECT * FROM emergency_contacts WHERE isActive = 1 ORDER BY isPrimary DESC, name ASC")
    fun getAllActiveContacts(): Flow<List<EmergencyContact>>
    
    @Query("SELECT * FROM emergency_contacts WHERE isActive = 1 ORDER BY isPrimary DESC, name ASC")
    fun getAllActiveContactsLiveData(): LiveData<List<EmergencyContact>>
    
    @Query("SELECT * FROM emergency_contacts WHERE isPrimary = 1 AND isActive = 1 LIMIT 1")
    suspend fun getPrimaryContact(): EmergencyContact?
    
    @Query("SELECT * FROM emergency_contacts WHERE id = :contactId")
    suspend fun getContactById(contactId: String): EmergencyContact?
    
    @Query("SELECT * FROM emergency_contacts WHERE phoneNumber = :phoneNumber AND isActive = 1")
    suspend fun getContactByPhoneNumber(phoneNumber: String): EmergencyContact?
    
    @Query("SELECT COUNT(*) FROM emergency_contacts WHERE isActive = 1")
    suspend fun getActiveContactsCount(): Int
    
    @Query("SELECT COUNT(*) FROM emergency_contacts WHERE isPrimary = 1 AND isActive = 1")
    suspend fun getPrimaryContactsCount(): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertContact(contact: EmergencyContact)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertContacts(contacts: List<EmergencyContact>)
    
    @Update
    suspend fun updateContact(contact: EmergencyContact)
    
    @Query("UPDATE emergency_contacts SET isPrimary = 0")
    suspend fun clearAllPrimaryFlags()
    
    @Query("UPDATE emergency_contacts SET isPrimary = 1 WHERE id = :contactId")
    suspend fun setPrimaryContact(contactId: String)
    
    @Query("UPDATE emergency_contacts SET isActive = 0 WHERE id = :contactId")
    suspend fun deactivateContact(contactId: String)
    
    @Delete
    suspend fun deleteContact(contact: EmergencyContact)
    
    @Query("DELETE FROM emergency_contacts WHERE id = :contactId")
    suspend fun deleteContactById(contactId: String)
    
    @Query("DELETE FROM emergency_contacts")
    suspend fun deleteAllContacts()
}
