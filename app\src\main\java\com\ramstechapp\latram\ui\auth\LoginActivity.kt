package com.ramstechapp.latram.ui.auth

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.ramstechapp.latram.MainActivity
import com.ramstechapp.latram.databinding.ActivityLoginBinding
import com.ramstechapp.latram.data.model.LoginRequest

class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    private lateinit var authViewModel: AuthViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        authViewModel = ViewModelProvider(this)[AuthViewModel::class.java]
        
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupClickListeners() {
        binding.apply {
            loginButton.setOnClickListener {
                performLogin()
            }
            
            forgotPasswordText.setOnClickListener {
                startActivity(Intent(this@LoginActivity, ForgotPasswordActivity::class.java))
            }
            
            registerText.setOnClickListener {
                startActivity(Intent(this@LoginActivity, RegisterActivity::class.java))
                finish()
            }
            
            backButton.setOnClickListener {
                finish()
            }
            
            emergencyAccessButton.setOnClickListener {
                // Direct emergency access
                val intent = Intent(this@LoginActivity, MainActivity::class.java).apply {
                    putExtra("emergency_mode", true)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                }
                startActivity(intent)
            }
        }
    }
    
    private fun observeViewModel() {
        authViewModel.authState.observe(this) { state ->
            when (state) {
                is AuthState.Loading -> {
                    showLoading(true)
                }
                is AuthState.Authenticated -> {
                    showLoading(false)
                    navigateToMain()
                }
                is AuthState.Error -> {
                    showLoading(false)
                    Toast.makeText(this, state.message, Toast.LENGTH_LONG).show()
                }
                is AuthState.Unauthenticated -> {
                    showLoading(false)
                }
            }
        }
    }
    
    private fun performLogin() {
        val email = binding.emailInput.text.toString().trim()
        val password = binding.passwordInput.text.toString()
        
        if (!validateInput(email, password)) {
            return
        }
        
        val loginRequest = LoginRequest(
            email = email,
            password = password,
            deviceId = getDeviceId(),
            fcmToken = getFcmToken()
        )
        
        authViewModel.login(loginRequest)
    }
    
    private fun validateInput(email: String, password: String): Boolean {
        if (email.isEmpty()) {
            binding.emailInputLayout.error = "Email is required"
            return false
        }
        
        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.emailInputLayout.error = "Please enter a valid email"
            return false
        }
        
        if (password.isEmpty()) {
            binding.passwordInputLayout.error = "Password is required"
            return false
        }
        
        if (password.length < 6) {
            binding.passwordInputLayout.error = "Password must be at least 6 characters"
            return false
        }
        
        // Clear errors
        binding.emailInputLayout.error = null
        binding.passwordInputLayout.error = null
        
        return true
    }
    
    private fun showLoading(show: Boolean) {
        binding.apply {
            progressBar.visibility = if (show) View.VISIBLE else View.GONE
            loginButton.isEnabled = !show
            emailInput.isEnabled = !show
            passwordInput.isEnabled = !show
        }
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(intent)
        finish()
    }
    
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        )
    }
    
    private fun getFcmToken(): String? {
        // TODO: Implement FCM token retrieval
        return null
    }
}
