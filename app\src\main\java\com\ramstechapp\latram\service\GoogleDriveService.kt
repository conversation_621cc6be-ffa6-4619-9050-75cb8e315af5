package com.ramstechapp.latram.service

import android.content.Context
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.api.client.extensions.android.http.AndroidHttp
import com.google.api.client.googleapis.extensions.android.gms.auth.GoogleAccountCredential
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.drive.Drive
import com.google.api.services.drive.DriveScopes
import com.google.api.services.drive.model.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.FileInputStream
import java.text.SimpleDateFormat
import java.util.*

class GoogleDriveService(private val context: Context) {
    
    companion object {
        private const val APPLICATION_NAME = "LATRAM Emergency App"
        private const val EMERGENCY_FOLDER_NAME = "LATRAM Emergency Records"
    }
    
    private var driveService: Drive? = null
    private var emergencyFolderId: String? = null
    
    suspend fun initializeDriveService(): Boolean = withContext(Dispatchers.IO) {
        try {
            val account = GoogleSignIn.getLastSignedInAccount(context)
            if (account == null) {
                return@withContext false
            }
            
            val credential = GoogleAccountCredential.usingOAuth2(
                context,
                listOf(DriveScopes.DRIVE_FILE)
            )
            credential.selectedAccount = account.account
            
            driveService = Drive.Builder(
                AndroidHttp.newCompatibleTransport(),
                GsonFactory(),
                credential
            )
                .setApplicationName(APPLICATION_NAME)
                .build()
            
            // Create or find emergency folder
            emergencyFolderId = createOrFindEmergencyFolder()
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun uploadEmergencyMedia(
        alertId: String,
        audioFilePath: String?,
        videoFilePath: String?
    ): String? = withContext(Dispatchers.IO) {
        
        if (driveService == null && !initializeDriveService()) {
            return@withContext null
        }
        
        val uploadedFiles = mutableListOf<String>()
        
        try {
            // Create alert-specific folder
            val alertFolderId = createAlertFolder(alertId)
            
            // Upload audio file
            audioFilePath?.let { path ->
                val audioFileId = uploadFile(
                    path,
                    "emergency_audio_${alertId}.3gp",
                    "audio/3gpp",
                    alertFolderId
                )
                audioFileId?.let { uploadedFiles.add("Audio: https://drive.google.com/file/d/$it/view") }
            }
            
            // Upload video file
            videoFilePath?.let { path ->
                val videoFileId = uploadFile(
                    path,
                    "emergency_video_${alertId}.mp4",
                    "video/mp4",
                    alertFolderId
                )
                videoFileId?.let { uploadedFiles.add("Video: https://drive.google.com/file/d/$it/view") }
            }
            
            if (uploadedFiles.isNotEmpty()) {
                "Emergency Media Files:\n${uploadedFiles.joinToString("\n")}"
            } else {
                null
            }
            
        } catch (e: Exception) {
            null
        }
    }
    
    private suspend fun createOrFindEmergencyFolder(): String? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null
            
            // Search for existing emergency folder
            val query = "name='$EMERGENCY_FOLDER_NAME' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            val result = drive.files().list()
                .setQ(query)
                .setSpaces("drive")
                .execute()
            
            if (result.files.isNotEmpty()) {
                return@withContext result.files[0].id
            }
            
            // Create new emergency folder
            val folderMetadata = File().apply {
                name = EMERGENCY_FOLDER_NAME
                mimeType = "application/vnd.google-apps.folder"
            }
            
            val folder = drive.files().create(folderMetadata)
                .setFields("id")
                .execute()
            
            folder.id
        } catch (e: Exception) {
            null
        }
    }
    
    private suspend fun createAlertFolder(alertId: String): String? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null
            val parentFolderId = emergencyFolderId ?: return@withContext null
            
            val timestamp = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault()).format(Date())
            val folderName = "Alert_${timestamp}_${alertId.take(8)}"
            
            val folderMetadata = File().apply {
                name = folderName
                mimeType = "application/vnd.google-apps.folder"
                parents = listOf(parentFolderId)
            }
            
            val folder = drive.files().create(folderMetadata)
                .setFields("id")
                .execute()
            
            folder.id
        } catch (e: Exception) {
            null
        }
    }
    
    private suspend fun uploadFile(
        filePath: String,
        fileName: String,
        mimeType: String,
        parentFolderId: String?
    ): String? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null
            val file = java.io.File(filePath)
            
            if (!file.exists()) {
                return@withContext null
            }
            
            val fileMetadata = File().apply {
                name = fileName
                parentFolderId?.let { parents = listOf(it) }
            }
            
            val mediaContent = com.google.api.client.http.FileContent(mimeType, file)
            
            val uploadedFile = drive.files().create(fileMetadata, mediaContent)
                .setFields("id")
                .execute()
            
            uploadedFile.id
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun deleteFile(fileId: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext false
            drive.files().delete(fileId).execute()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun listEmergencyFiles(): List<File>? = withContext(Dispatchers.IO) {
        try {
            val drive = driveService ?: return@withContext null
            val folderId = emergencyFolderId ?: return@withContext null
            
            val query = "'$folderId' in parents and trashed=false"
            val result = drive.files().list()
                .setQ(query)
                .setSpaces("drive")
                .setFields("files(id,name,createdTime,size)")
                .execute()
            
            result.files
        } catch (e: Exception) {
            null
        }
    }
    
    fun isSignedIn(): Boolean {
        return GoogleSignIn.getLastSignedInAccount(context) != null
    }
    
    fun getSignInOptions(): GoogleSignInOptions {
        return GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .requestScopes(com.google.android.gms.common.api.Scope(DriveScopes.DRIVE_FILE))
            .build()
    }
}
