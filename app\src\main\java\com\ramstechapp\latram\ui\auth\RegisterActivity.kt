package com.ramstechapp.latram.ui.auth

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.ramstechapp.latram.MainActivity
import com.ramstechapp.latram.databinding.ActivityRegisterBinding
import com.ramstechapp.latram.data.model.RegisterRequest

class RegisterActivity : AppCompatActivity() {

    private lateinit var binding: ActivityRegisterBinding
    private lateinit var authViewModel: AuthViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityRegisterBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        authViewModel = ViewModelProvider(this)[AuthViewModel::class.java]
        
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupClickListeners() {
        binding.apply {
            registerButton.setOnClickListener {
                performRegistration()
            }
            
            loginText.setOnClickListener {
                startActivity(Intent(this@RegisterActivity, LoginActivity::class.java))
                finish()
            }
            
            backButton.setOnClickListener {
                finish()
            }
            
            termsCheckbox.setOnCheckedChangeListener { _, isChecked ->
                registerButton.isEnabled = isChecked && validateAllFields()
            }
            
            emergencyAccessButton.setOnClickListener {
                // Direct emergency access
                val intent = Intent(this@RegisterActivity, MainActivity::class.java).apply {
                    putExtra("emergency_mode", true)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                }
                startActivity(intent)
            }
        }
    }
    
    private fun observeViewModel() {
        authViewModel.authState.observe(this) { state ->
            when (state) {
                is AuthState.Loading -> {
                    showLoading(true)
                }
                is AuthState.Authenticated -> {
                    showLoading(false)
                    // Show email verification prompt
                    showEmailVerificationDialog()
                }
                is AuthState.Error -> {
                    showLoading(false)
                    Toast.makeText(this, state.message, Toast.LENGTH_LONG).show()
                }
                is AuthState.Unauthenticated -> {
                    showLoading(false)
                }
            }
        }
    }
    
    private fun performRegistration() {
        val fullName = binding.fullNameInput.text.toString().trim()
        val email = binding.emailInput.text.toString().trim()
        val phoneNumber = binding.phoneInput.text.toString().trim()
        val password = binding.passwordInput.text.toString()
        val confirmPassword = binding.confirmPasswordInput.text.toString()
        
        if (!validateInput(fullName, email, phoneNumber, password, confirmPassword)) {
            return
        }
        
        val registerRequest = RegisterRequest(
            email = email,
            password = password,
            fullName = fullName,
            phoneNumber = phoneNumber.ifEmpty { null },
            deviceId = getDeviceId(),
            fcmToken = getFcmToken()
        )
        
        authViewModel.register(registerRequest)
    }
    
    private fun validateInput(
        fullName: String,
        email: String,
        phoneNumber: String,
        password: String,
        confirmPassword: String
    ): Boolean {
        var isValid = true
        
        if (fullName.isEmpty()) {
            binding.fullNameInputLayout.error = "Full name is required"
            isValid = false
        } else if (fullName.length < 2) {
            binding.fullNameInputLayout.error = "Full name must be at least 2 characters"
            isValid = false
        } else {
            binding.fullNameInputLayout.error = null
        }
        
        if (email.isEmpty()) {
            binding.emailInputLayout.error = "Email is required"
            isValid = false
        } else if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.emailInputLayout.error = "Please enter a valid email"
            isValid = false
        } else {
            binding.emailInputLayout.error = null
        }
        
        if (phoneNumber.isNotEmpty() && !android.util.Patterns.PHONE.matcher(phoneNumber).matches()) {
            binding.phoneInputLayout.error = "Please enter a valid phone number"
            isValid = false
        } else {
            binding.phoneInputLayout.error = null
        }
        
        if (password.isEmpty()) {
            binding.passwordInputLayout.error = "Password is required"
            isValid = false
        } else if (password.length < 8) {
            binding.passwordInputLayout.error = "Password must be at least 8 characters"
            isValid = false
        } else if (!password.matches(Regex(".*[A-Z].*"))) {
            binding.passwordInputLayout.error = "Password must contain at least one uppercase letter"
            isValid = false
        } else if (!password.matches(Regex(".*[a-z].*"))) {
            binding.passwordInputLayout.error = "Password must contain at least one lowercase letter"
            isValid = false
        } else if (!password.matches(Regex(".*\\d.*"))) {
            binding.passwordInputLayout.error = "Password must contain at least one number"
            isValid = false
        } else {
            binding.passwordInputLayout.error = null
        }
        
        if (confirmPassword.isEmpty()) {
            binding.confirmPasswordInputLayout.error = "Please confirm your password"
            isValid = false
        } else if (password != confirmPassword) {
            binding.confirmPasswordInputLayout.error = "Passwords do not match"
            isValid = false
        } else {
            binding.confirmPasswordInputLayout.error = null
        }
        
        if (!binding.termsCheckbox.isChecked) {
            Toast.makeText(this, "Please accept the terms and conditions", Toast.LENGTH_SHORT).show()
            isValid = false
        }
        
        return isValid
    }
    
    private fun validateAllFields(): Boolean {
        val fullName = binding.fullNameInput.text.toString().trim()
        val email = binding.emailInput.text.toString().trim()
        val phoneNumber = binding.phoneInput.text.toString().trim()
        val password = binding.passwordInput.text.toString()
        val confirmPassword = binding.confirmPasswordInput.text.toString()
        
        return fullName.isNotEmpty() && email.isNotEmpty() && 
               password.isNotEmpty() && confirmPassword.isNotEmpty() &&
               password == confirmPassword && password.length >= 8
    }
    
    private fun showLoading(show: Boolean) {
        binding.apply {
            progressBar.visibility = if (show) View.VISIBLE else View.GONE
            registerButton.isEnabled = !show && termsCheckbox.isChecked
            fullNameInput.isEnabled = !show
            emailInput.isEnabled = !show
            phoneInput.isEnabled = !show
            passwordInput.isEnabled = !show
            confirmPasswordInput.isEnabled = !show
        }
    }
    
    private fun showEmailVerificationDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Email Verification")
            .setMessage("A verification email has been sent to your email address. Please verify your email to complete registration.")
            .setPositiveButton("Continue") { _, _ ->
                navigateToMain()
            }
            .setNegativeButton("Resend Email") { _, _ ->
                authViewModel.resendVerification(binding.emailInput.text.toString().trim())
            }
            .setCancelable(false)
            .show()
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(intent)
        finish()
    }
    
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        )
    }
    
    private fun getFcmToken(): String? {
        // TODO: Implement FCM token retrieval
        return null
    }
}
