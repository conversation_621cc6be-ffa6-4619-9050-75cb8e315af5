<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary"
    tools:context=".ui.auth.WelcomeActivity">

    <!-- App Logo -->
    <ImageView
        android:id="@+id/appLogo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="80dp"
        android:src="@drawable/ic_emergency"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white" />

    <!-- App Name -->
    <TextView
        android:id="@+id/appName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="LATRAM"
        android:textAppearance="@style/TextAppearance.Material3.DisplayMedium"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appLogo" />

    <!-- App Tagline -->
    <TextView
        android:id="@+id/appTagline"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="32dp"
        android:gravity="center"
        android:text="Your Emergency Response Companion"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="@color/white"
        android:alpha="0.9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appName" />

    <!-- Features List -->
    <LinearLayout
        android:id="@+id/featuresLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="32dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appTagline">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_check_circle"
                app:tint="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Instant Emergency Alerts"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_check_circle"
                app:tint="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Stealth Recording & Evidence"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_check_circle"
                app:tint="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Multiple Emergency Triggers"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_check_circle"
                app:tint="@color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Real-time Location Sharing"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/white" />

        </LinearLayout>

    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Login Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/loginButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            android:text="Login"
            android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
            app:backgroundTint="@color/white"
            app:cornerRadius="28dp"
            android:textColor="@color/primary" />

        <!-- Register Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/registerButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            android:text="Create Account"
            android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
            android:textColor="@color/white"
            app:cornerRadius="28dp"
            app:strokeColor="@color/white"
            app:strokeWidth="2dp" />

        <!-- Skip Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/skipButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="8dp"
            android:text="Continue as Guest"
            android:textColor="@color/white"
            android:alpha="0.8" />

        <!-- Emergency Access Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/emergencyAccessButton"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="🚨 Emergency Access"
            android:textColor="@color/emergency_red_light"
            android:textStyle="bold" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
