package com.ramstechapp.latram.service

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.*
import kotlin.coroutines.resume

class CallService(private val context: Context) : TextToSpeech.OnInitListener {
    
    private var textToSpeech: TextToSpeech? = null
    private var isTtsInitialized = false
    
    init {
        initializeTextToSpeech()
    }
    
    private fun initializeTextToSpeech() {
        textToSpeech = TextToSpeech(context, this)
    }
    
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            textToSpeech?.let { tts ->
                val result = tts.setLanguage(Locale.getDefault())
                isTtsInitialized = result != TextToSpeech.LANG_MISSING_DATA && 
                                 result != TextToSpeech.LANG_NOT_SUPPORTED
                
                // Set speech rate and pitch for emergency situations
                tts.setSpeechRate(0.8f) // Slightly slower for clarity
                tts.setPitch(1.0f)
            }
        }
    }
    
    suspend fun makeEmergencyCall(phoneNumber: String): Boolean {
        return try {
            if (!hasCallPermission()) {
                false
            } else {
                val callIntent = Intent(Intent.ACTION_CALL).apply {
                    data = Uri.parse("tel:$phoneNumber")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(callIntent)
                true
            }
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun makeEmergencyCallWithMessage(
        phoneNumber: String,
        message: String,
        delayBeforeMessage: Long = 5000L // 5 seconds delay
    ): Boolean {
        val callSuccess = makeEmergencyCall(phoneNumber)
        
        if (callSuccess && isTtsInitialized) {
            // Wait for call to connect before speaking
            delay(delayBeforeMessage)
            speakEmergencyMessage(message)
        }
        
        return callSuccess
    }
    
    suspend fun speakEmergencyMessage(message: String): Boolean = suspendCancellableCoroutine { continuation ->
        if (!isTtsInitialized) {
            continuation.resume(false)
            return@suspendCancellableCoroutine
        }
        
        val utteranceId = "emergency_message_${System.currentTimeMillis()}"
        
        textToSpeech?.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                // TTS started speaking
            }
            
            override fun onDone(utteranceId: String?) {
                continuation.resume(true)
            }
            
            override fun onError(utteranceId: String?) {
                continuation.resume(false)
            }
        })
        
        val formattedMessage = formatMessageForTTS(message)
        val result = textToSpeech?.speak(
            formattedMessage,
            TextToSpeech.QUEUE_FLUSH,
            null,
            utteranceId
        )
        
        if (result != TextToSpeech.SUCCESS) {
            continuation.resume(false)
        }
    }
    
    private fun formatMessageForTTS(message: String): String {
        return buildString {
            append("Emergency alert. ")
            append(message.replace("🚨", "Emergency. "))
            append(". Please check the location sent via SMS for my exact position. ")
            append("This is an automated emergency message from LATRAM app.")
        }
    }
    
    fun stopSpeaking() {
        textToSpeech?.stop()
    }
    
    fun isSpeaking(): Boolean {
        return textToSpeech?.isSpeaking ?: false
    }
    
    private fun hasCallPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.CALL_PHONE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    fun canMakeCall(): Boolean {
        return hasCallPermission()
    }
    
    fun isTtsAvailable(): Boolean {
        return isTtsInitialized
    }
    
    fun destroy() {
        textToSpeech?.stop()
        textToSpeech?.shutdown()
        textToSpeech = null
        isTtsInitialized = false
    }
}
