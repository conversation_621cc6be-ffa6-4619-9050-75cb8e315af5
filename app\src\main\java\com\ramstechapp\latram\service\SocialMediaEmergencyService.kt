package com.ramstechapp.latram.service

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Location
import android.net.Uri
import java.net.URLEncoder

/**
 * Social Media Emergency Service
 * Shares emergency alerts via popular messaging apps:
 * - WhatsApp
 * - Telegram
 * - Facebook Messenger
 * - Twitter
 */
class SocialMediaEmergencyService(private val context: Context) {
    
    /**
     * Share emergency alert via WhatsApp
     */
    fun shareViaWhatsApp(
        phoneNumber: String,
        message: String,
        location: Location?
    ): Boolean {
        return try {
            if (!isWhatsAppInstalled()) {
                return false
            }
            
            val formattedMessage = formatEmergencyMessage(message, location)
            val encodedMessage = URLEncoder.encode(formattedMessage, "UTF-8")
            
            // Format phone number for WhatsApp (remove + and spaces)
            val cleanNumber = phoneNumber.replace("+", "").replace(" ", "").replace("-", "")
            
            val whatsappIntent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://wa.me/$cleanNumber?text=$encodedMessage")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            
            context.startActivity(whatsappIntent)
            true
            
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Share emergency alert via Telegram
     */
    fun shareViaTelegram(
        username: String,
        message: String,
        location: Location?
    ): Boolean {
        return try {
            if (!isTelegramInstalled()) {
                return false
            }
            
            val formattedMessage = formatEmergencyMessage(message, location)
            val encodedMessage = URLEncoder.encode(formattedMessage, "UTF-8")
            
            val telegramIntent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://t.me/$username?text=$encodedMessage")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            
            context.startActivity(telegramIntent)
            true
            
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Share emergency location via Google Maps sharing
     */
    fun shareLocationViaGoogleMaps(location: Location): Boolean {
        return try {
            val mapsIntent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://maps.google.com/?q=${location.latitude},${location.longitude}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            
            context.startActivity(mapsIntent)
            true
            
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Share emergency via any messaging app (generic share)
     */
    fun shareViaGenericMessaging(
        message: String,
        location: Location?
    ): Boolean {
        return try {
            val formattedMessage = formatEmergencyMessage(message, location)
            
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, formattedMessage)
                putExtra(Intent.EXTRA_SUBJECT, "🚨 EMERGENCY ALERT")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            
            val chooserIntent = Intent.createChooser(shareIntent, "Share Emergency Alert")
            chooserIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            
            context.startActivity(chooserIntent)
            true
            
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Share emergency via multiple platforms simultaneously
     */
    fun shareViaMultiplePlatforms(
        phoneNumber: String,
        message: String,
        location: Location?
    ): List<String> {
        val successfulShares = mutableListOf<String>()
        
        // Try WhatsApp first
        if (shareViaWhatsApp(phoneNumber, message, location)) {
            successfulShares.add("WhatsApp")
        }
        
        // Try generic sharing as fallback
        if (shareViaGenericMessaging(message, location)) {
            successfulShares.add("Generic Share")
        }
        
        return successfulShares
    }
    
    /**
     * Create emergency broadcast message for social media
     */
    fun createEmergencyBroadcast(
        message: String,
        location: Location?,
        includeHashtags: Boolean = true
    ): String {
        val formattedMessage = formatEmergencyMessage(message, location)
        
        return if (includeHashtags) {
            "$formattedMessage\n\n#Emergency #Help #LATRAM #SafetyAlert"
        } else {
            formattedMessage
        }
    }
    
    private fun formatEmergencyMessage(message: String, location: Location?): String {
        val timestamp = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
            .format(java.util.Date())
        
        return buildString {
            append("🚨 EMERGENCY ALERT 🚨\n\n")
            append("$message\n\n")
            append("Time: $timestamp\n")
            
            if (location != null) {
                append("Location: https://maps.google.com/?q=${location.latitude},${location.longitude}\n")
                append("Coordinates: ${location.latitude}, ${location.longitude}\n")
                append("Accuracy: ±${location.accuracy.toInt()}m\n")
            }
            
            append("\nSent via LATRAM Emergency App")
            append("\nPlease check my location and send help!")
        }
    }
    
    private fun isWhatsAppInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo("com.whatsapp", PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            try {
                // Try WhatsApp Business
                context.packageManager.getPackageInfo("com.whatsapp.w4b", PackageManager.GET_ACTIVITIES)
                true
            } catch (e: PackageManager.NameNotFoundException) {
                false
            }
        }
    }
    
    private fun isTelegramInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo("org.telegram.messenger", PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    private fun isFacebookMessengerInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo("com.facebook.orca", PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * Get list of available messaging apps
     */
    fun getAvailableMessagingApps(): List<String> {
        val availableApps = mutableListOf<String>()
        
        if (isWhatsAppInstalled()) availableApps.add("WhatsApp")
        if (isTelegramInstalled()) availableApps.add("Telegram")
        if (isFacebookMessengerInstalled()) availableApps.add("Facebook Messenger")
        
        return availableApps
    }
}
