<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.home.HomeFragment">

    <!-- Status Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/statusCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/statusTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Emergency Status"
                android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                android:textColor="@color/primary" />

            <TextView
                android:id="@+id/statusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Ready for emergency"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge" />

            <TextView
                android:id="@+id/lastAlertText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="No recent alerts"
                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                android:textColor="@color/on_surface_variant" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Emergency Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/emergencyButton"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:contentDescription="Emergency Button"
        android:src="@drawable/ic_emergency"
        app:backgroundTint="@color/emergency_red"
        app:fabSize="auto"
        app:layout_constraintBottom_toTopOf="@+id/emergencyTypeSpinner"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusCard"
        app:tint="@android:color/white" />

    <TextView
        android:id="@+id/emergencyButtonLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="EMERGENCY"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
        android:textColor="@color/emergency_red"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/emergencyButton"
        app:layout_constraintStart_toStartOf="@+id/emergencyButton"
        app:layout_constraintTop_toBottomOf="@+id/emergencyButton" />

    <!-- Emergency Type Selector -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/emergencyTypeLayout"
        style="@style/Widget.Material3.TextInputLayout.ExposedDropdownMenu"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:hint="Emergency Type"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emergencyButtonLabel">

        <AutoCompleteTextView
            android:id="@+id/emergencyTypeSpinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:text="General Emergency" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Custom Message Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/customMessageLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="Custom Emergency Message (Optional)"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emergencyTypeLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/customMessageInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:maxLines="3" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Quick Actions -->
    <LinearLayout
        android:id="@+id/quickActionsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/customMessageLayout">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/testLocationButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="Test Location"
            app:icon="@drawable/ic_location" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/viewContactsButton"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="Contacts"
            app:icon="@drawable/ic_contacts" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
