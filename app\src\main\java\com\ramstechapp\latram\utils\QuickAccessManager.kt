package com.ramstechapp.latram.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.ramstechapp.latram.R
import com.ramstechapp.latram.receiver.BootReceiver
import com.ramstechapp.latram.service.FloatingButtonService
import com.ramstechapp.latram.ui.emergency.EmergencyLockScreenActivity

class QuickAccessManager(private val context: Context) {
    
    companion object {
        private const val PERSISTENT_NOTIFICATION_ID = 3001
        private const val PERSISTENT_CHANNEL_ID = "emergency_quick_access"
        private const val OVERLAY_PERMISSION_REQUEST = 1001
    }
    
    init {
        createPersistentNotificationChannel()
    }
    
    /**
     * Enable floating button overlay
     */
    fun enableFloatingButton(): Boolean {
        return if (canDrawOverlays()) {
            val intent = Intent(context, FloatingButtonService::class.java).apply {
                action = FloatingButtonService.ACTION_START_FLOATING_BUTTON
            }
            context.startForegroundService(intent)
            BootReceiver.setFloatingButtonEnabled(context, true)
            true
        } else {
            false
        }
    }
    
    /**
     * Disable floating button overlay
     */
    fun disableFloatingButton() {
        val intent = Intent(context, FloatingButtonService::class.java).apply {
            action = FloatingButtonService.ACTION_STOP_FLOATING_BUTTON
        }
        context.startService(intent)
        BootReceiver.setFloatingButtonEnabled(context, false)
    }
    
    /**
     * Show persistent notification for quick access
     */
    fun showPersistentNotification() {
        val emergencyIntent = Intent(context, EmergencyLockScreenActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val emergencyPendingIntent = PendingIntent.getActivity(
            context, 0, emergencyIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, PERSISTENT_CHANNEL_ID)
            .setContentTitle("Emergency Quick Access")
            .setContentText("Tap for emergency alert")
            .setSmallIcon(R.drawable.ic_emergency)
            .setContentIntent(emergencyPendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setShowWhen(false)
            .addAction(
                R.drawable.ic_emergency,
                "EMERGENCY",
                emergencyPendingIntent
            )
            .build()
        
        NotificationManagerCompat.from(context).notify(PERSISTENT_NOTIFICATION_ID, notification)
        BootReceiver.setQuickAccessEnabled(context, true)
    }
    
    /**
     * Hide persistent notification
     */
    fun hidePersistentNotification() {
        NotificationManagerCompat.from(context).cancel(PERSISTENT_NOTIFICATION_ID)
        BootReceiver.setQuickAccessEnabled(context, false)
    }
    
    /**
     * Check if overlay permission is granted
     */
    fun canDrawOverlays(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }
    
    /**
     * Request overlay permission
     */
    fun requestOverlayPermission(): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !canDrawOverlays()) {
            Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = android.net.Uri.parse("package:${context.packageName}")
            }
        } else {
            null
        }
    }
    
    /**
     * Request battery optimization exemption
     */
    fun requestBatteryOptimizationExemption(): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = android.net.Uri.parse("package:${context.packageName}")
            }
        } else {
            null
        }
    }
    
    /**
     * Check if battery optimization is disabled
     */
    fun isBatteryOptimizationDisabled(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true
        }
    }
    
    /**
     * Enable all quick access features
     */
    fun enableAllQuickAccess(): List<String> {
        val issues = mutableListOf<String>()
        
        // Check overlay permission
        if (!canDrawOverlays()) {
            issues.add("Overlay permission required for floating button")
        } else {
            enableFloatingButton()
        }
        
        // Check battery optimization
        if (!isBatteryOptimizationDisabled()) {
            issues.add("Battery optimization should be disabled")
        }
        
        // Always show persistent notification
        showPersistentNotification()
        
        return issues
    }
    
    /**
     * Disable all quick access features
     */
    fun disableAllQuickAccess() {
        disableFloatingButton()
        hidePersistentNotification()
    }
    
    /**
     * Get current quick access status
     */
    fun getQuickAccessStatus(): QuickAccessStatus {
        return QuickAccessStatus(
            floatingButtonEnabled = BootReceiver.isFloatingButtonEnabled(context),
            persistentNotificationEnabled = BootReceiver.isQuickAccessEnabled(context),
            overlayPermissionGranted = canDrawOverlays(),
            batteryOptimizationDisabled = isBatteryOptimizationDisabled()
        )
    }
    
    private fun createPersistentNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                PERSISTENT_CHANNEL_ID,
                "Emergency Quick Access",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Persistent notification for emergency quick access"
                setShowBadge(true)
                enableVibration(false)
                setSound(null, null)
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
            }
            
            val notificationManager = context.getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
}

data class QuickAccessStatus(
    val floatingButtonEnabled: Boolean,
    val persistentNotificationEnabled: Boolean,
    val overlayPermissionGranted: Boolean,
    val batteryOptimizationDisabled: Boolean
) {
    val isFullyEnabled: Boolean
        get() = floatingButtonEnabled && persistentNotificationEnabled && 
                overlayPermissionGranted && batteryOptimizationDisabled
}
