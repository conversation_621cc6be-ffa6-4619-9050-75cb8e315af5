-- LATRAM Emergency App - Supabase Database Setup
-- Run this script in your Supabase SQL editor

-- Create the alerts table
CREATE TABLE IF NOT EXISTS alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    location TEXT NOT NULL,
    latitude DOUBLE PRECISION NOT NULL,
    longitude DOUBLE PRECISION NOT NULL,
    message TEXT NOT NULL,
    date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    media_link TEXT,
    contact_called TEXT NOT NULL,
    emergency_type TEXT DEFAULT 'GENERAL' CHECK (emergency_type IN ('GENERAL', 'MEDICAL', 'FIRE', 'POLICE', 'ACCIDENT', 'NATURAL_DISASTER')),
    sms_delivered BOOLEAN DEFAULT FALSE,
    call_made BOOLEAN DEFAULT FALSE,
    media_uploaded BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_alerts_date_time ON alerts(date_time DESC);
CREATE INDEX IF NOT EXISTS idx_alerts_user_id ON alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_alerts_emergency_type ON alerts(emergency_type);
CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts(created_at DESC);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_alerts_updated_at 
    BEFORE UPDATE ON alerts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;

-- Create policies for alerts table
-- Allow anonymous inserts (for emergency situations)
CREATE POLICY "Allow anonymous inserts" ON alerts
    FOR INSERT 
    WITH CHECK (true);

-- Allow users to read their own alerts (if user_id is set)
CREATE POLICY "Users can read own alerts" ON alerts
    FOR SELECT 
    USING (auth.uid() = user_id OR user_id IS NULL);

-- Allow users to update their own alerts
CREATE POLICY "Users can update own alerts" ON alerts
    FOR UPDATE 
    USING (auth.uid() = user_id OR user_id IS NULL);

-- Create storage bucket for emergency media
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'emergency-media',
    'emergency-media',
    false,
    52428800, -- 50MB limit
    ARRAY['audio/mpeg', 'audio/wav', 'audio/3gpp', 'video/mp4', 'video/3gpp', 'video/quicktime']
) ON CONFLICT (id) DO NOTHING;

-- Create storage policies
CREATE POLICY "Allow authenticated uploads" ON storage.objects
    FOR INSERT 
    WITH CHECK (bucket_id = 'emergency-media' AND auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated downloads" ON storage.objects
    FOR SELECT 
    USING (bucket_id = 'emergency-media' AND auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated updates" ON storage.objects
    FOR UPDATE 
    USING (bucket_id = 'emergency-media' AND auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated deletes" ON storage.objects
    FOR DELETE 
    USING (bucket_id = 'emergency-media' AND auth.role() = 'authenticated');

-- Create a view for alert statistics
CREATE OR REPLACE VIEW alert_statistics AS
SELECT 
    COUNT(*) as total_alerts,
    COUNT(*) FILTER (WHERE sms_delivered = true) as sms_delivered_count,
    COUNT(*) FILTER (WHERE call_made = true) as calls_made_count,
    COUNT(*) FILTER (WHERE media_uploaded = true) as media_uploaded_count,
    COUNT(*) FILTER (WHERE date_time >= NOW() - INTERVAL '24 hours') as alerts_last_24h,
    COUNT(*) FILTER (WHERE date_time >= NOW() - INTERVAL '7 days') as alerts_last_week,
    COUNT(*) FILTER (WHERE date_time >= NOW() - INTERVAL '30 days') as alerts_last_month,
    emergency_type,
    COUNT(*) as count_by_type
FROM alerts
GROUP BY emergency_type;

-- Create a function to clean up old alerts (optional)
CREATE OR REPLACE FUNCTION cleanup_old_alerts(days_to_keep INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM alerts 
    WHERE created_at < NOW() - (days_to_keep || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON alerts TO anon, authenticated;
GRANT SELECT ON alert_statistics TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_alerts TO authenticated;

-- Insert sample emergency types for reference
COMMENT ON COLUMN alerts.emergency_type IS 'Type of emergency: GENERAL, MEDICAL, FIRE, POLICE, ACCIDENT, NATURAL_DISASTER';

-- Create notification function for real-time updates (optional)
CREATE OR REPLACE FUNCTION notify_alert_insert()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM pg_notify('alert_inserted', row_to_json(NEW)::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER alert_insert_notification
    AFTER INSERT ON alerts
    FOR EACH ROW
    EXECUTE FUNCTION notify_alert_insert();

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'LATRAM Emergency App database setup completed successfully!';
    RAISE NOTICE 'Tables created: alerts';
    RAISE NOTICE 'Storage bucket created: emergency-media';
    RAISE NOTICE 'Policies and triggers configured';
    RAISE NOTICE 'Remember to update your app configuration with the Supabase URL and API keys';
END $$;
