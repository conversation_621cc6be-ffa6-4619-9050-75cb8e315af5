package com.ramstechapp.latram.ui.history

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.ramstechapp.latram.LatramApplication

class HistoryViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = (application as LatramApplication).emergencyRepository

    val alerts = repository.getAllAlerts()

    private val _showToast = MutableLiveData<com.ramstechapp.latram.utils.Event<String>>()
    val showToast: LiveData<com.ramstechapp.latram.utils.Event<String>> = _showToast

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
}
