package com.ramstechapp.latram.ui.history

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.ramstechapp.latram.LatramApplication

class HistoryViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = (application as LatramApplication).emergencyRepository

    private val _text = MutableLiveData<String>().apply {
        value = "Emergency History will be displayed here"
    }
    val text: LiveData<String> = _text
}
