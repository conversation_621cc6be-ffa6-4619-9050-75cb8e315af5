package com.ramstechapp.latram.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.ramstechapp.latram.data.model.User
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {

    @Query("SELECT * FROM users WHERE id = :userId LIMIT 1")
    suspend fun getUserById(userId: String): User?

    @Query("SELECT * FROM users WHERE email = :email LIMIT 1")
    suspend fun getUserByEmail(email: String): User?

    @Query("SELECT * FROM users WHERE phoneNumber = :phoneNumber LIMIT 1")
    suspend fun getUserByPhone(phoneNumber: String): User?

    @Query("SELECT * FROM users WHERE isActive = 1 LIMIT 1")
    fun getCurrentUser(): Flow<User?>

    @Query("SELECT * FROM users WHERE isActive = 1 LIMIT 1")
    fun getCurrentUserLiveData(): LiveData<User?>

    @Query("SELECT * FROM users WHERE isActive = 1 LIMIT 1")
    suspend fun getCurrentUserSync(): User?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User)

    @Update
    suspend fun updateUser(user: User)

    @Delete
    suspend fun deleteUser(user: User)

    @Query("UPDATE users SET isActive = 0")
    suspend fun deactivateAllUsers()

    @Query("UPDATE users SET isActive = 1 WHERE id = :userId")
    suspend fun activateUser(userId: String)

    @Query("UPDATE users SET lastLoginAt = :loginTime WHERE id = :userId")
    suspend fun updateLastLogin(userId: String, loginTime: Long)

    @Query("UPDATE users SET isEmailVerified = :verified WHERE id = :userId")
    suspend fun updateEmailVerification(userId: String, verified: Boolean)

    @Query("UPDATE users SET isPhoneVerified = :verified WHERE id = :userId")
    suspend fun updatePhoneVerification(userId: String, verified: Boolean)

    @Query("UPDATE users SET passwordResetToken = :token, passwordResetExpiry = :expiry WHERE email = :email")
    suspend fun setPasswordResetToken(email: String, token: String?, expiry: Long?)

    @Query("SELECT * FROM users WHERE passwordResetToken = :token AND passwordResetExpiry > :currentTime LIMIT 1")
    suspend fun getUserByResetToken(token: String, currentTime: Long): User?

    @Query("UPDATE users SET passwordResetToken = NULL, passwordResetExpiry = NULL WHERE id = :userId")
    suspend fun clearPasswordResetToken(userId: String)

    @Query("UPDATE users SET emergencyContactsSetup = :setup WHERE id = :userId")
    suspend fun updateEmergencyContactsSetup(userId: String, setup: Boolean)

    @Query("UPDATE users SET quickAccessEnabled = :enabled WHERE id = :userId")
    suspend fun updateQuickAccessEnabled(userId: String, enabled: Boolean)

    @Query("UPDATE users SET stealthModeEnabled = :enabled WHERE id = :userId")
    suspend fun updateStealthModeEnabled(userId: String, enabled: Boolean)

    @Query("UPDATE users SET profileImageUrl = :imageUrl WHERE id = :userId")
    suspend fun updateProfileImage(userId: String, imageUrl: String?)

    @Query("UPDATE users SET fullName = :fullName WHERE id = :userId")
    suspend fun updateFullName(userId: String, fullName: String)

    @Query("UPDATE users SET phoneNumber = :phoneNumber WHERE id = :userId")
    suspend fun updatePhoneNumber(userId: String, phoneNumber: String?)

    @Query("UPDATE users SET accountType = :accountType, subscriptionExpiry = :expiry WHERE id = :userId")
    suspend fun updateSubscription(userId: String, accountType: String, expiry: Long?)

    @Query("SELECT COUNT(*) FROM users WHERE isActive = 1")
    suspend fun getActiveUsersCount(): Int

    @Query("DELETE FROM users WHERE isActive = 0 AND createdAt < :cutoffTime")
    suspend fun deleteInactiveUsers(cutoffTime: Long)

    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()
}
