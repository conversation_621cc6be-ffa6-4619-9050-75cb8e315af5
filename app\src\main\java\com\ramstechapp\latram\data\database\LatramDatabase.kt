package com.ramstechapp.latram.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.ramstechapp.latram.data.database.dao.EmergencyAlertDao
import com.ramstechapp.latram.data.database.dao.EmergencyContactDao
import com.ramstechapp.latram.data.database.dao.UserSettingsDao
import com.ramstechapp.latram.data.database.dao.UserDao
import com.ramstechapp.latram.data.model.EmergencyAlert
import com.ramstechapp.latram.data.model.EmergencyContact
import com.ramstechapp.latram.data.model.UserSettings
import com.ramstechapp.latram.data.model.User

@Database(
    entities = [
        EmergencyAlert::class,
        EmergencyContact::class,
        UserSettings::class,
        User::class
    ],
    version = 2,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class LatramDatabase : RoomDatabase() {

    abstract fun emergencyAlertDao(): EmergencyAlertDao
    abstract fun emergencyContactDao(): EmergencyContactDao
    abstract fun userSettingsDao(): UserSettingsDao
    abstract fun userDao(): UserDao

    companion object {
        @Volatile
        private var INSTANCE: LatramDatabase? = null

        fun getDatabase(context: Context): LatramDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    LatramDatabase::class.java,
                    "latram_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
