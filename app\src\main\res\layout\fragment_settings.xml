<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.settings.SettingsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/settingsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Settings"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
            android:textColor="@color/primary" />

        <!-- Emergency Contacts Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/settings_emergency_contacts"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/manageContactsButton"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Manage Emergency Contacts"
                    app:icon="@drawable/ic_contacts" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Emergency Settings Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Emergency Settings"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <!-- Default Message -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/settings_default_message">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/defaultMessageInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLines="3" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Auto Call Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/autoCallSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_auto_call" />

                <!-- Auto Record Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/autoRecordSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_auto_record" />

                <!-- Google Drive Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/googleDriveSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_google_drive" />

                <!-- TTS Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/ttsSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_text_to_speech" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Stealth Settings Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Stealth Recording"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <!-- Stealth Recording Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/stealthRecordingSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_stealth_recording" />

                <!-- Real-time Upload Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/realTimeUploadSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_real_time_upload" />

                <!-- Silent Recording Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/silentRecordingSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_silent_recording" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Quick Access Settings Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Quick Access Features"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <!-- Floating Button Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/floatingButtonSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Floating Emergency Button" />

                <!-- Lock Screen Access Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/lockScreenAccessSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Lock Screen Access" />

                <!-- Gesture Detection Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/gestureDetectionSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/gesture_detection_enabled" />

                <!-- Voice Commands Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/voiceCommandsSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/voice_command_enabled" />

                <!-- Power Button Emergency Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/powerButtonEmergencySwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/power_button_emergency_enabled" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Social Media Settings Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Social Media Sharing"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <!-- WhatsApp Sharing Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/whatsappSharingSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/share_via_whatsapp" />

                <!-- Telegram Sharing Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/telegramSharingSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/share_via_telegram" />

                <!-- Social Media Broadcast Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/socialMediaBroadcastSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/social_sharing_enabled" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Backup Settings Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Backup and Sync"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <!-- Auto Backup Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/autoBackupSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Auto Backup Contacts" />

                <!-- Backup to Google Drive Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/backupToGoogleDriveSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Backup to Google Drive" />

                <!-- Backup to Supabase Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/backupToSupabaseSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Backup to Cloud" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/testFeaturesButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Test Features"
                app:icon="@drawable/ic_test" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/enableAllFeaturesButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Enable All"
                app:icon="@drawable/ic_check_circle" />

        </LinearLayout>

        <!-- Save Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/saveSettingsButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/button_save"
            app:icon="@drawable/ic_settings" />

    </LinearLayout>

</ScrollView>
