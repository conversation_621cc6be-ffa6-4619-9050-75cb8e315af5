package com.ramstechapp.latram.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.*

@Entity(tableName = "emergency_contacts")
data class EmergencyContact(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    
    val name: String,
    val phoneNumber: String,
    val relationship: String = "",
    val email: String? = null,
    
    val isPrimary: Boolean = false,
    val isActive: Boolean = true,
    val priority: Int = 0, // 0 = highest priority
    
    val lastContactedAt: Date? = null,
    val contactCount: Int = 0,
    val isVerified: Boolean = false,
    
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)
