package com.ramstechapp.latram.utils

import android.content.Context
import android.content.SharedPreferences
import android.media.AudioManager
import android.os.Build
import android.provider.Settings

class StealthManager(private val context: Context) {
    
    companion object {
        private const val PREFS_NAME = "latram_stealth_prefs"
        private const val KEY_STEALTH_RECORDING_ENABLED = "stealth_recording_enabled"
        private const val KEY_REAL_TIME_UPLOAD_ENABLED = "real_time_upload_enabled"
        private const val KEY_AUTO_DELETE_AFTER_UPLOAD = "auto_delete_after_upload"
        private const val KEY_STEALTH_QUALITY = "stealth_quality"
        private const val KEY_SILENT_RECORDING = "silent_recording"
        private const val KEY_BACKGROUND_RECORDING = "background_recording"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    /**
     * Enable/disable stealth recording
     */
    fun setStealthRecordingEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_STEALTH_RECORDING_ENABLED, enabled).apply()
    }
    
    fun isStealthRecordingEnabled(): Boolean {
        return prefs.getBoolean(KEY_STEALTH_RECORDING_ENABLED, true) // Default enabled for security
    }
    
    /**
     * Enable/disable real-time upload to Drive
     */
    fun setRealTimeUploadEnabled(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_REAL_TIME_UPLOAD_ENABLED, enabled).apply()
    }
    
    fun isRealTimeUploadEnabled(): Boolean {
        return prefs.getBoolean(KEY_REAL_TIME_UPLOAD_ENABLED, true) // Default enabled
    }
    
    /**
     * Enable/disable auto-delete after successful upload
     */
    fun setAutoDeleteAfterUpload(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_AUTO_DELETE_AFTER_UPLOAD, enabled).apply()
    }
    
    fun isAutoDeleteAfterUploadEnabled(): Boolean {
        return prefs.getBoolean(KEY_AUTO_DELETE_AFTER_UPLOAD, false) // Default keep files
    }
    
    /**
     * Set stealth recording quality (LOW, MEDIUM, HIGH)
     */
    fun setStealthQuality(quality: StealthQuality) {
        prefs.edit().putString(KEY_STEALTH_QUALITY, quality.name).apply()
    }
    
    fun getStealthQuality(): StealthQuality {
        val qualityName = prefs.getString(KEY_STEALTH_QUALITY, StealthQuality.LOW.name)
        return try {
            StealthQuality.valueOf(qualityName ?: StealthQuality.LOW.name)
        } catch (e: Exception) {
            StealthQuality.LOW
        }
    }
    
    /**
     * Enable/disable silent recording (no camera sounds, indicators)
     */
    fun setSilentRecording(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_SILENT_RECORDING, enabled).apply()
    }
    
    fun isSilentRecordingEnabled(): Boolean {
        return prefs.getBoolean(KEY_SILENT_RECORDING, true) // Default silent for stealth
    }
    
    /**
     * Enable/disable background recording (continue even when app is closed)
     */
    fun setBackgroundRecording(enabled: Boolean) {
        prefs.edit().putBoolean(KEY_BACKGROUND_RECORDING, enabled).apply()
    }
    
    fun isBackgroundRecordingEnabled(): Boolean {
        return prefs.getBoolean(KEY_BACKGROUND_RECORDING, true) // Default enabled
    }
    
    /**
     * Configure device for stealth recording
     */
    fun configureDeviceForStealth(): List<String> {
        val issues = mutableListOf<String>()
        
        try {
            // Disable camera sounds if possible
            if (isSilentRecordingEnabled()) {
                disableCameraSounds()
            }
            
            // Check if device supports stealth recording
            if (!canRecordInBackground()) {
                issues.add("Background recording may be limited on this device")
            }
            
            // Check storage space
            if (!hasEnoughStorageSpace()) {
                issues.add("Low storage space - recordings may fail")
            }
            
            // Check network for real-time upload
            if (isRealTimeUploadEnabled() && !hasNetworkConnection()) {
                issues.add("No network connection - real-time upload disabled")
            }
            
        } catch (e: Exception) {
            issues.add("Failed to configure stealth settings")
        }
        
        return issues
    }
    
    /**
     * Disable camera shutter sounds for stealth recording
     */
    private fun disableCameraSounds() {
        try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            
            // Mute camera sounds temporarily
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                audioManager.adjustStreamVolume(AudioManager.STREAM_SYSTEM, AudioManager.ADJUST_MUTE, 0)
            }
        } catch (e: Exception) {
            // Silent failure - don't compromise stealth
        }
    }
    
    /**
     * Re-enable camera sounds after recording
     */
    fun restoreCameraSounds() {
        try {
            val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                audioManager.adjustStreamVolume(AudioManager.STREAM_SYSTEM, AudioManager.ADJUST_UNMUTE, 0)
            }
        } catch (e: Exception) {
            // Silent failure
        }
    }
    
    /**
     * Check if device can record in background
     */
    private fun canRecordInBackground(): Boolean {
        return try {
            // Check if background app restrictions are disabled
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
                powerManager.isIgnoringBatteryOptimizations(context.packageName)
            } else {
                true
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Check available storage space
     */
    private fun hasEnoughStorageSpace(): Boolean {
        return try {
            val externalDir = context.getExternalFilesDir(null)
            val freeSpace = externalDir?.freeSpace ?: 0
            freeSpace > 100 * 1024 * 1024 // At least 100MB free
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Check network connection
     */
    private fun hasNetworkConnection(): Boolean {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val activeNetwork = connectivityManager.activeNetworkInfo
            activeNetwork?.isConnected == true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get stealth recording configuration
     */
    fun getStealthConfiguration(): StealthConfiguration {
        return StealthConfiguration(
            stealthRecordingEnabled = isStealthRecordingEnabled(),
            realTimeUploadEnabled = isRealTimeUploadEnabled(),
            autoDeleteAfterUpload = isAutoDeleteAfterUploadEnabled(),
            quality = getStealthQuality(),
            silentRecording = isSilentRecordingEnabled(),
            backgroundRecording = isBackgroundRecordingEnabled()
        )
    }
    
    /**
     * Apply stealth configuration
     */
    fun applyStealthConfiguration(config: StealthConfiguration) {
        setStealthRecordingEnabled(config.stealthRecordingEnabled)
        setRealTimeUploadEnabled(config.realTimeUploadEnabled)
        setAutoDeleteAfterUpload(config.autoDeleteAfterUpload)
        setStealthQuality(config.quality)
        setSilentRecording(config.silentRecording)
        setBackgroundRecording(config.backgroundRecording)
    }
    
    /**
     * Reset to default stealth settings
     */
    fun resetToDefaults() {
        prefs.edit().clear().apply()
    }
}

enum class StealthQuality {
    LOW,    // Minimal quality for maximum stealth
    MEDIUM, // Balanced quality and stealth
    HIGH    // Best quality (less stealthy)
}

data class StealthConfiguration(
    val stealthRecordingEnabled: Boolean,
    val realTimeUploadEnabled: Boolean,
    val autoDeleteAfterUpload: Boolean,
    val quality: StealthQuality,
    val silentRecording: Boolean,
    val backgroundRecording: Boolean
)
