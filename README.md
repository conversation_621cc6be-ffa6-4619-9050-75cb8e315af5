# LATRAM Emergency App

LATRAM is an emergency alert mobile application designed to help users quickly send distress signals in critical situations.

## Features

### Core Emergency Features
- **One-Button Emergency Alert**: Quick emergency trigger with customizable messages
- **Automatic SMS**: Send location, time, and custom emergency message to contacts
- **Emergency Calling**: Automatic call with Text-to-Speech message reading
- **Media Recording**: Record audio/video during emergencies
- **Location Sharing**: GPS coordinates and Google Maps links
- **Google Drive Upload**: Automatic media backup to user's Drive

### Data Management
- **Local Storage**: SQLite database with Room for offline functionality
- **Cloud Sync**: Supabase backend for remote storage and sync
- **Emergency History**: Complete log of all emergency alerts
- **Contact Management**: Multiple emergency contacts with primary selection

### Safety Features
- **Background Service**: Continues operation even if app is closed
- **Foreground Notifications**: Clear status during emergency operations
- **Permission Management**: Comprehensive permission handling
- **Offline Capability**: Works without internet for SMS and calls

### Quick Access Features
- **Floating Emergency Button**: Always-visible overlay button for instant access
- **Lock Screen Access**: Emergency functionality available even when phone is locked
- **Persistent Notification**: Quick access from notification panel
- **Boot Auto-Start**: Automatically enables quick access after device restart
- **Battery Optimization Bypass**: Ensures emergency features work reliably

### Stealth Recording Features
- **Covert Recording**: Records audio/video without visible indicators or sounds
- **Real-time Upload**: Streams recordings directly to Google Drive during capture
- **Hidden Storage**: Local files stored in hidden directories with encrypted names
- **Silent Operation**: Disables camera sounds and minimizes visual feedback
- **Background Recording**: Continues recording even if device is compromised
- **Evidence Preservation**: Ensures recordings survive even if device is damaged or confiscated

## Technical Architecture

### Frontend (Android)
- **Language**: Kotlin
- **Architecture**: MVVM with LiveData and ViewBinding
- **Database**: Room (SQLite)
- **UI**: Material Design 3 components
- **Navigation**: Navigation Component

### Backend (Supabase)
- **Database**: PostgreSQL
- **Storage**: Supabase Storage
- **API**: REST with auto-generated endpoints
- **Authentication**: Optional Supabase Auth

### Key Libraries
- **Location**: Google Play Services Location
- **Maps**: Google Maps SDK
- **Drive**: Google Drive API
- **Networking**: Retrofit + OkHttp
- **Permissions**: Dexter
- **Media**: Android MediaRecorder and Camera2

## Setup Instructions

### 1. Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24+ (Android 7.0)
- Google Play Services
- Supabase account

### 2. Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)

2. Create the alerts table:
```sql
CREATE TABLE alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    location TEXT NOT NULL,
    latitude DOUBLE PRECISION NOT NULL,
    longitude DOUBLE PRECISION NOT NULL,
    message TEXT NOT NULL,
    date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    media_link TEXT,
    contact_called TEXT NOT NULL,
    emergency_type TEXT DEFAULT 'GENERAL',
    sms_delivered BOOLEAN DEFAULT FALSE,
    call_made BOOLEAN DEFAULT FALSE,
    media_uploaded BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

3. Create storage bucket for media:
```sql
INSERT INTO storage.buckets (id, name, public) VALUES ('emergency-media', 'emergency-media', false);
```

4. Update `SupabaseConfig.kt` with your project details:
```kotlin
const val SUPABASE_URL = "https://your-project-id.supabase.co"
const val SUPABASE_ANON_KEY = "your-anon-key-here"
```

### 3. Google Services Setup

1. Create a project in [Google Cloud Console](https://console.cloud.google.com)

2. Enable the following APIs:
   - Maps SDK for Android
   - Google Drive API
   - Places API (optional)

3. Create credentials:
   - API Key for Maps
   - OAuth 2.0 Client ID for Drive

4. Add your Maps API key to `local.properties`:
```
MAPS_API_KEY=your_maps_api_key_here
```

5. Add your OAuth client configuration to `app/google-services.json`

### 4. Build and Run

1. Clone the repository:
```bash
git clone <repository-url>
cd LATRAM
```

2. Open in Android Studio

3. Sync project with Gradle files

4. Build and run on device or emulator

## Configuration

### Emergency Contacts
- Add at least one emergency contact before using the app
- Set a primary contact for automatic alerts
- Supports multiple contacts with relationships

### Settings
- **Default Message**: Customize emergency message
- **Auto Call**: Enable automatic calling after SMS
- **Auto Record**: Enable media recording during emergencies
- **Google Drive**: Enable cloud backup of recordings
- **Text-to-Speech**: Enable voice message during calls
- **Recording Duration**: Set recording length (10-60 seconds)

### Permissions Required
- **SMS**: Send emergency messages
- **Phone**: Make emergency calls
- **Location**: Share precise location
- **Camera**: Record emergency videos
- **Microphone**: Record emergency audio
- **Storage**: Save and access media files
- **Display Over Apps**: Show floating emergency button
- **Appear on Lock Screen**: Emergency access when phone is locked
- **Ignore Battery Optimization**: Ensure reliable operation

## Usage

### Quick Emergency Access (Recommended)
1. **Floating Button**: Tap the floating red button visible on your screen
2. **Lock Screen**: Double-tap the emergency notification or use the floating button
3. **Notification Panel**: Pull down notifications and tap "Emergency Quick Access"

### Basic Emergency Alert
1. Open the app
2. Press the large red EMERGENCY button
3. The app will automatically:
   - Get your current location
   - Send SMS to primary contact
   - Make emergency call (if enabled)
   - Record media (if enabled)
   - Upload to Google Drive (if enabled)

### Custom Emergency
1. Select emergency type from dropdown
2. Enter custom message (optional)
3. Press EMERGENCY button

### View History
1. Go to History tab
2. View all past emergency alerts
3. See status of SMS, calls, and media uploads
4. Access recorded media files

## Security and Privacy

- All data is stored locally first
- Cloud sync is optional and user-controlled
- Media files are encrypted during upload
- No personal data is shared without consent
- Emergency contacts are stored securely

## Troubleshooting

### Common Issues

1. **Location not found**
   - Ensure GPS is enabled
   - Grant location permissions
   - Try moving to an open area

2. **SMS not sending**
   - Check SMS permissions
   - Verify phone number format
   - Ensure cellular connection

3. **Google Drive upload fails**
   - Sign in to Google account
   - Check internet connection
   - Verify Drive permissions

4. **App crashes during emergency**
   - Update to latest version
   - Clear app cache
   - Restart device

### Support
For technical support or bug reports, please create an issue in the repository.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Disclaimer

This app is designed to assist in emergency situations but should not be relied upon as the sole means of emergency communication. Always have backup emergency plans and contact methods available.
