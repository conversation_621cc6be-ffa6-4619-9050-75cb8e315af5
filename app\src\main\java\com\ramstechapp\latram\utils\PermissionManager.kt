package com.ramstechapp.latram.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.karumi.dexter.Dexter
import com.karumi.dexter.MultiplePermissionsReport
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.multi.MultiplePermissionsListener

class PermissionManager(private val context: Context) {
    
    companion object {
        const val PERMISSION_REQUEST_CODE = 1001
        
        val REQUIRED_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.SEND_SMS,
                Manifest.permission.CALL_PHONE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.CAMERA,
                Manifest.permission.READ_MEDIA_AUDIO,
                Manifest.permission.READ_MEDIA_VIDEO,
                Manifest.permission.POST_NOTIFICATIONS
            )
        } else {
            arrayOf(
                Manifest.permission.SEND_SMS,
                Manifest.permission.CALL_PHONE,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.CAMERA,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
        
        val CRITICAL_PERMISSIONS = arrayOf(
            Manifest.permission.SEND_SMS,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    }
    
    fun checkAllPermissions(): Boolean {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    fun checkCriticalPermissions(): Boolean {
        return CRITICAL_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    fun getMissingPermissions(): List<String> {
        return REQUIRED_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }
    
    fun getMissingCriticalPermissions(): List<String> {
        return CRITICAL_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }
    
    fun requestAllPermissions(
        activity: Activity,
        onPermissionsGranted: () -> Unit,
        onPermissionsDenied: (List<String>) -> Unit,
        onPermissionsPermanentlyDenied: (List<String>) -> Unit
    ) {
        Dexter.withContext(activity)
            .withPermissions(*REQUIRED_PERMISSIONS)
            .withListener(object : MultiplePermissionsListener {
                override fun onPermissionsChecked(report: MultiplePermissionsReport) {
                    when {
                        report.areAllPermissionsGranted() -> {
                            onPermissionsGranted()
                        }
                        report.isAnyPermissionPermanentlyDenied -> {
                            val permanentlyDenied = report.deniedPermissionResponses
                                .filter { it.isPermanentlyDenied }
                                .map { it.permissionName }
                            onPermissionsPermanentlyDenied(permanentlyDenied)
                        }
                        else -> {
                            val denied = report.deniedPermissionResponses
                                .map { it.permissionName }
                            onPermissionsDenied(denied)
                        }
                    }
                }
                
                override fun onPermissionRationaleShouldBeShown(
                    permissions: List<PermissionRequest>,
                    token: PermissionToken
                ) {
                    token.continuePermissionRequest()
                }
            })
            .check()
    }
    
    fun requestCriticalPermissions(
        activity: Activity,
        onPermissionsGranted: () -> Unit,
        onPermissionsDenied: (List<String>) -> Unit
    ) {
        Dexter.withContext(activity)
            .withPermissions(*CRITICAL_PERMISSIONS)
            .withListener(object : MultiplePermissionsListener {
                override fun onPermissionsChecked(report: MultiplePermissionsReport) {
                    if (report.areAllPermissionsGranted()) {
                        onPermissionsGranted()
                    } else {
                        val denied = report.deniedPermissionResponses
                            .map { it.permissionName }
                        onPermissionsDenied(denied)
                    }
                }
                
                override fun onPermissionRationaleShouldBeShown(
                    permissions: List<PermissionRequest>,
                    token: PermissionToken
                ) {
                    token.continuePermissionRequest()
                }
            })
            .check()
    }
    
    fun getPermissionDescription(permission: String): String {
        return when (permission) {
            Manifest.permission.SEND_SMS -> "Send SMS messages for emergency alerts"
            Manifest.permission.CALL_PHONE -> "Make emergency phone calls"
            Manifest.permission.ACCESS_FINE_LOCATION -> "Access precise location for emergency alerts"
            Manifest.permission.ACCESS_COARSE_LOCATION -> "Access approximate location"
            Manifest.permission.RECORD_AUDIO -> "Record audio during emergencies"
            Manifest.permission.CAMERA -> "Record video during emergencies"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "Read media files"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "Save emergency recordings"
            Manifest.permission.READ_MEDIA_AUDIO -> "Access audio files"
            Manifest.permission.READ_MEDIA_VIDEO -> "Access video files"
            Manifest.permission.POST_NOTIFICATIONS -> "Show emergency notifications"
            else -> "Required for app functionality"
        }
    }
    
    fun isPermissionCritical(permission: String): Boolean {
        return permission in CRITICAL_PERMISSIONS
    }
}
