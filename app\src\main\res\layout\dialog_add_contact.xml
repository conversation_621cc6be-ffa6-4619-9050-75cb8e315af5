<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Name Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Contact Name"
        app:startIconDrawable="@drawable/ic_person">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/nameInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPersonName" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Phone Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Phone Number"
        app:startIconDrawable="@drawable/ic_phone">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/phoneInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="phone" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Relationship Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Relationship (Optional)"
        app:startIconDrawable="@drawable/ic_family">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/relationshipInput"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Primary Contact Switch -->
    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/primaryContactSwitch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Set as Primary Contact"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="Primary contact will be called first during emergencies"
        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
        android:textColor="@color/on_surface_variant" />

</LinearLayout>
