package com.ramstechapp.latram.ui

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.ramstechapp.latram.R
import com.ramstechapp.latram.databinding.ActivityMainBinding
import com.ramstechapp.latram.service.EmergencyService
import com.ramstechapp.latram.ui.viewmodel.MainViewModel
import com.ramstechapp.latram.utils.PermissionManager
import com.ramstechapp.latram.utils.QuickAccessManager

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var permissionManager: PermissionManager
    private lateinit var quickAccessManager: QuickAccessManager
    private val viewModel: MainViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        permissionManager = PermissionManager(this)
        quickAccessManager = QuickAccessManager(this)

        setupNavigation()
        checkPermissions()
        setupQuickAccess()
        observeViewModel()
    }

    private fun setupNavigation() {
        val navView: BottomNavigationView = binding.navView
        val navController = findNavController(R.id.nav_host_fragment_activity_main)

        val appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.navigation_home,
                R.id.navigation_history,
                R.id.navigation_settings
            )
        )

        setupActionBarWithNavController(navController, appBarConfiguration)
        navView.setupWithNavController(navController)
    }

    private fun checkPermissions() {
        if (!permissionManager.checkCriticalPermissions()) {
            requestCriticalPermissions()
        } else if (!permissionManager.checkAllPermissions()) {
            requestAllPermissions()
        } else {
            // All basic permissions granted, check overlay permission
            checkOverlayPermission()
        }
    }

    private fun setupQuickAccess() {
        // Setup persistent notification for quick access
        quickAccessManager.showPersistentNotification()
    }

    private fun checkOverlayPermission() {
        if (!permissionManager.canDrawOverlays()) {
            showOverlayPermissionDialog()
        } else {
            // All permissions granted, enable quick access features
            enableQuickAccessFeatures()
        }
    }

    private fun showOverlayPermissionDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Enable Quick Emergency Access")
            .setMessage("Allow LATRAM to display over other apps for quick emergency access?\n\n" +
                       "This enables:\n" +
                       "• Floating emergency button\n" +
                       "• Emergency access from lock screen\n" +
                       "• Quick access from any app")
            .setPositiveButton("Enable") { _, _ ->
                permissionManager.requestOverlayPermission(this)
            }
            .setNegativeButton("Skip") { _, _ ->
                Toast.makeText(this, "Quick access features disabled", Toast.LENGTH_SHORT).show()
            }
            .show()
    }

    private fun enableQuickAccessFeatures() {
        val issues = quickAccessManager.enableAllQuickAccess()
        if (issues.isNotEmpty()) {
            showQuickAccessIssues(issues)
        } else {
            Toast.makeText(this, "Quick emergency access enabled", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showQuickAccessIssues(issues: List<String>) {
        val message = "Some quick access features need attention:\n\n" +
                     issues.joinToString("\n• ", "• ")

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Quick Access Setup")
            .setMessage(message)
            .setPositiveButton("Fix Issues") { _, _ ->
                // Open settings to fix issues
                if (!quickAccessManager.isBatteryOptimizationDisabled()) {
                    quickAccessManager.requestBatteryOptimizationExemption()?.let { intent ->
                        startActivity(intent)
                    }
                }
            }
            .setNegativeButton("Continue") { _, _ ->
                // Continue with limited functionality
            }
            .show()
    }

    private fun requestCriticalPermissions() {
        permissionManager.requestCriticalPermissions(
            activity = this,
            onPermissionsGranted = {
                Toast.makeText(this, "Critical permissions granted", Toast.LENGTH_SHORT).show()
                // Request remaining permissions
                if (!permissionManager.checkAllPermissions()) {
                    requestAllPermissions()
                }
            },
            onPermissionsDenied = { deniedPermissions ->
                showCriticalPermissionDialog(deniedPermissions)
            }
        )
    }

    private fun requestAllPermissions() {
        permissionManager.requestAllPermissions(
            activity = this,
            onPermissionsGranted = {
                Toast.makeText(this, "All permissions granted", Toast.LENGTH_SHORT).show()
                viewModel.onPermissionsGranted()
            },
            onPermissionsDenied = { deniedPermissions ->
                showPermissionDialog(deniedPermissions, false)
            },
            onPermissionsPermanentlyDenied = { permanentlyDenied ->
                showPermissionDialog(permanentlyDenied, true)
            }
        )
    }

    private fun showCriticalPermissionDialog(deniedPermissions: List<String>) {
        val message = buildString {
            append("Critical permissions are required for emergency functionality:\n\n")
            deniedPermissions.forEach { permission ->
                append("• ${permissionManager.getPermissionDescription(permission)}\n")
            }
            append("\nThe app cannot function without these permissions.")
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Critical Permissions Required")
            .setMessage(message)
            .setPositiveButton("Grant Permissions") { _, _ ->
                requestCriticalPermissions()
            }
            .setNegativeButton("Exit App") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    private fun showPermissionDialog(deniedPermissions: List<String>, isPermanentlyDenied: Boolean) {
        val title = if (isPermanentlyDenied) "Permissions Permanently Denied" else "Permissions Required"
        val message = buildString {
            if (isPermanentlyDenied) {
                append("Some permissions have been permanently denied. Please enable them in Settings:\n\n")
            } else {
                append("The following permissions are needed for full functionality:\n\n")
            }

            deniedPermissions.forEach { permission ->
                append("• ${permissionManager.getPermissionDescription(permission)}\n")
            }
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(if (isPermanentlyDenied) "Open Settings" else "Retry") { _, _ ->
                if (isPermanentlyDenied) {
                    openAppSettings()
                } else {
                    requestAllPermissions()
                }
            }
            .setNegativeButton("Continue") { _, _ ->
                // Continue with limited functionality
                viewModel.onPermissionsPartiallyGranted()
            }
            .show()
    }

    private fun openAppSettings() {
        val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = android.net.Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }

    private fun observeViewModel() {
        viewModel.emergencyTriggerEvent.observe(this) { event ->
            event.getContentIfNotHandled()?.let { (message, emergencyType) ->
                triggerEmergency(message, emergencyType.name)
            }
        }

        viewModel.showToast.observe(this) { event ->
            event.getContentIfNotHandled()?.let { message ->
                Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun triggerEmergency(message: String, emergencyType: String) {
        val intent = Intent(this, EmergencyService::class.java).apply {
            action = EmergencyService.ACTION_TRIGGER_EMERGENCY
            putExtra(EmergencyService.EXTRA_EMERGENCY_MESSAGE, message)
            putExtra(EmergencyService.EXTRA_EMERGENCY_TYPE, emergencyType)
        }
        startForegroundService(intent)
    }

    override fun onResume() {
        super.onResume()
        // Check if permissions were granted in settings
        if (permissionManager.checkAllPermissions()) {
            viewModel.onPermissionsGranted()

            // Check overlay permission and enable quick access if granted
            if (permissionManager.canDrawOverlays()) {
                enableQuickAccessFeatures()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            PermissionManager.OVERLAY_PERMISSION_REQUEST -> {
                if (permissionManager.canDrawOverlays()) {
                    enableQuickAccessFeatures()
                } else {
                    Toast.makeText(this, "Overlay permission denied. Quick access disabled.", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
}
