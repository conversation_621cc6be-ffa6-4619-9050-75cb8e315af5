<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.settings.SettingsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/settingsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Settings"
            android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
            android:textColor="@color/primary" />

        <!-- Emergency Contacts Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/settings_emergency_contacts"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/manageContactsButton"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Manage Emergency Contacts"
                    app:icon="@drawable/ic_contacts" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Emergency Settings Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Emergency Settings"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall" />

                <!-- Default Message -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/settings_default_message">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/defaultMessageInput"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLines="3" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Auto Call Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/autoCallSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/settings_auto_call" />

                <!-- Auto Record Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/autoRecordSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_auto_record" />

                <!-- Google Drive Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/googleDriveSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_google_drive" />

                <!-- TTS Switch -->
                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/ttsSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/settings_text_to_speech" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Save Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/saveSettingsButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/button_save"
            app:icon="@drawable/ic_settings" />

    </LinearLayout>

</ScrollView>
