package com.ramstechapp.latram.data.database.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.ramstechapp.latram.data.model.UserSettings
import kotlinx.coroutines.flow.Flow

@Dao
interface UserSettingsDao {

    @Query("SELECT * FROM user_settings WHERE id = 'default_settings' LIMIT 1")
    fun getSettings(): Flow<UserSettings?>

    @Query("SELECT * FROM user_settings WHERE id = 'default_settings' LIMIT 1")
    fun getSettingsLiveData(): LiveData<UserSettings?>

    @Query("SELECT * FROM user_settings WHERE id = 'default_settings' LIMIT 1")
    suspend fun getSettingsSync(): UserSettings?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: UserSettings)

    @Update
    suspend fun updateSettings(settings: UserSettings)

    @Query("UPDATE user_settings SET defaultMessage = :message WHERE id = 'default_settings'")
    suspend fun updateDefaultMessage(message: String)

    @Query("UPDATE user_settings SET autoCallEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateAutoCallEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET autoRecordEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateAutoRecordEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET googleDriveEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateGoogleDriveEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET ttsEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateTtsEnabled(enabled: Boolean)

    // Stealth Recording Settings
    @Query("UPDATE user_settings SET stealthRecordingEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateStealthRecordingEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET realTimeUploadEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateRealTimeUploadEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET silentRecordingEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateSilentRecordingEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET autoDeleteAfterUpload = :enabled WHERE id = 'default_settings'")
    suspend fun updateAutoDeleteAfterUpload(enabled: Boolean)

    // Quick Access Settings
    @Query("UPDATE user_settings SET floatingButtonEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateFloatingButtonEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET lockScreenAccessEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateLockScreenAccessEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET gestureDetectionEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateGestureDetectionEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET voiceCommandsEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateVoiceCommandsEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET powerButtonEmergencyEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updatePowerButtonEmergencyEnabled(enabled: Boolean)

    // Social Media Settings
    @Query("UPDATE user_settings SET whatsappSharingEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateWhatsappSharingEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET telegramSharingEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateTelegramSharingEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET socialMediaBroadcastEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateSocialMediaBroadcastEnabled(enabled: Boolean)

    // Backup Settings
    @Query("UPDATE user_settings SET autoBackupEnabled = :enabled WHERE id = 'default_settings'")
    suspend fun updateAutoBackupEnabled(enabled: Boolean)

    @Query("UPDATE user_settings SET backupToGoogleDrive = :enabled WHERE id = 'default_settings'")
    suspend fun updateBackupToGoogleDrive(enabled: Boolean)

    @Query("UPDATE user_settings SET backupToSupabase = :enabled WHERE id = 'default_settings'")
    suspend fun updateBackupToSupabase(enabled: Boolean)

    @Query("UPDATE user_settings SET recordingDuration = :duration WHERE id = 'default_settings'")
    suspend fun updateRecordingDuration(duration: Int)

    @Query("UPDATE user_settings SET lastBackupDate = :date WHERE id = 'default_settings'")
    suspend fun updateLastBackupDate(date: Long)

    @Delete
    suspend fun deleteSettings(settings: UserSettings)

    @Query("DELETE FROM user_settings")
    suspend fun deleteAllSettings()
}
